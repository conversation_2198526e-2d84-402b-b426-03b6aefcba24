# Construction GraphQL Tool Documentation

## Overview

The Construction GraphQL Tool is a LangGraph tool that enables natural language queries against a Nest.js GraphQL backend to retrieve construction project data. It can dynamically interpret user queries and convert them into appropriate GraphQL queries for projects, tasks, milestones, and resources.

## Features

- **Natural Language Processing**: Converts natural language queries into GraphQL queries
- **Construction Domain Focus**: Specifically designed for construction project management data
- **Multiple Data Types**: Supports queries for projects, tasks, milestones, and resources
- **Intelligent Filtering**: Automatically applies filters based on query context
- **Async Support**: Fully asynchronous implementation for high performance

## Installation

The tool is automatically included when you install the project dependencies:

```bash
pip install -e .
```

## Configuration

### Environment Variables

Set the following environment variables:

```bash
# Required: GraphQL endpoint URL
GRAPHQL_ENDPOINT=http://localhost:3000/graphql

# Optional: Authentication token for GraphQL API
GRAPHQL_AUTH_TOKEN=your-jwt-token-here
```

### Expected GraphQL Schema

Your Nest.js backend should support the following GraphQL schema structure:

```graphql
type Project {
  id: ID!
  name: String!
  description: String
  status: ProjectStatus!
  startDate: String
  endDate: String
  budget: Float
  location: String
  projectManager: String
  tasks: [Task!]
  milestones: [Milestone!]
  resources: [Resource!]
  createdAt: String
  updatedAt: String
}

type Task {
  id: ID!
  name: String!
  description: String
  status: TaskStatus!
  priority: TaskPriority!
  assignedTo: String
  startDate: String
  endDate: String
  estimatedHours: Float
  actualHours: Float
  progress: Float
  dependencies: [String!]
  project: Project!
  comments: [Comment!]
}

type Milestone {
  id: ID!
  name: String!
  description: String
  targetDate: String
  completionDate: String
  status: MilestoneStatus!
  criteria: String
  dependencies: [String!]
  project: Project!
  tasks: [Task!]
}

type Resource {
  id: ID!
  name: String!
  type: ResourceType!
  quantity: Float!
  unit: String
  costPerUnit: Float
  totalCost: Float
  supplier: String
  availability: ResourceAvailability!
  location: String
  project: Project!
  allocations: [ResourceAllocation!]
}

enum ProjectStatus {
  ACTIVE
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
}

enum MilestoneStatus {
  PENDING
  COMPLETED
  OVERDUE
}

enum ResourceType {
  EQUIPMENT
  MATERIAL
  LABOR
}

enum ResourceAvailability {
  AVAILABLE
  UNAVAILABLE
}
```

## Usage Examples

### Basic Queries

```python
from app.core.langgraph.tools.construction_graphql_tool import ConstructionGraphQLTool

tool = ConstructionGraphQLTool()

# Project queries
result = await tool._arun("Show me all active projects")
result = await tool._arun("Get completed projects")

# Task queries
result = await tool._arun("Find all high priority tasks")
result = await tool._arun("Show me overdue tasks")
result = await tool._arun("Get tasks assigned to John")

# Milestone queries
result = await tool._arun("Show me upcoming milestones")
result = await tool._arun("Find completed milestones")

# Resource queries
result = await tool._arun("Show me available equipment")
result = await tool._arun("Find all materials")
```

### Advanced Queries

```python
# Project-specific queries
result = await tool._arun(
    "Show me all tasks for project ABC123",
    project_id="ABC123"
)

# Limited results
result = await tool._arun(
    "Show me the top 5 active projects",
    limit=5
)

# Complex queries
result = await tool._arun("Find high priority overdue tasks assigned to Sarah")
result = await tool._arun("Show me upcoming milestones in the next 14 days")
result = await tool._arun("Get available equipment resources")
```

## Natural Language Query Patterns

The tool recognizes the following query patterns:

### Project Queries
- "Show me all active projects"
- "Get completed projects"
- "Find projects on hold"
- "Show me projects with tasks"

### Task Queries
- "Find all high priority tasks"
- "Show me overdue tasks"
- "Get tasks assigned to [name]"
- "Find completed tasks"
- "Show me tasks in progress"

### Milestone Queries
- "Show me upcoming milestones"
- "Find completed milestones"
- "Get milestones due in [X] days"
- "Show me overdue milestones"

### Resource Queries
- "Show me available resources"
- "Find equipment resources"
- "Get materials for project"
- "Show me labor resources"

## Integration with LangGraph

The tool is automatically available in your LangGraph agent:

```python
from app.core.langgraph.graph import LangGraphAgent

agent = LangGraphAgent()

# The tool will be automatically called when users ask about construction data
# Example user query: "What are our current active projects?"
response = await agent.get_response([
    {"role": "user", "content": "What are our current active projects?"}
], session_id="test-session")
```

## Error Handling

The tool includes comprehensive error handling:

- **Connection Errors**: Graceful handling of network issues
- **GraphQL Errors**: Detailed error reporting for GraphQL query failures
- **Authentication Errors**: Clear messaging for auth token issues
- **Schema Mismatches**: Helpful error messages for schema incompatibilities

## Testing

Use the included test script to verify the tool works with your backend:

```bash
python test_graphql_tool.py
```

This will test query parsing and GraphQL query generation without making actual API calls.

## Troubleshooting

### Common Issues

1. **"GraphQL request failed" Error**
   - Check that GRAPHQL_ENDPOINT is correctly set
   - Verify your Nest.js backend is running
   - Ensure the GraphQL endpoint is accessible

2. **"GraphQL errors" in Response**
   - Verify your GraphQL schema matches the expected structure
   - Check if authentication is required and GRAPHQL_AUTH_TOKEN is set
   - Review the specific GraphQL error messages in the logs

3. **"No data returned" Message**
   - Ensure your database has test data
   - Check that the query filters are not too restrictive
   - Verify the GraphQL resolvers are working correctly

### Debug Mode

Enable debug logging to see detailed query execution:

```python
import logging
logging.getLogger('app.core.langgraph.tools.construction_graphql_tool').setLevel(logging.DEBUG)
```

## Customization

### Adding New Query Types

To add support for new query types:

1. Update the `_parse_natural_language_query` method to recognize new patterns
2. Add corresponding query builders in `ConstructionProjectQueryBuilder`
3. Update the `_format_results` method to handle new data types

### Modifying Query Patterns

Edit the keyword matching logic in `_parse_natural_language_query` to support different query patterns or languages.

### Custom Formatting

Override the `_format_results` method to customize how results are displayed to users.

## Security Considerations

- Always use HTTPS endpoints in production
- Store authentication tokens securely
- Implement proper rate limiting on your GraphQL endpoint
- Validate and sanitize all user inputs
- Use environment variables for sensitive configuration

## Performance Tips

- Use appropriate `limit` parameters to avoid large result sets
- Implement GraphQL query caching in your backend
- Consider using GraphQL subscriptions for real-time updates
- Monitor query complexity and implement query depth limiting
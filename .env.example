# Environment Configuration Example

# Application Settings
APP_ENV=development
PROJECT_NAME="Web Assistant"
VERSION=1.0.0
DEBUG=true

# API Settings
API_V1_STR=/api/v1

# CORS Settings
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8000"

# Langfuse Settings
LANGFUSE_PUBLIC_KEY="your-langfuse-public-key"
LANGFUSE_SECRET_KEY="your-langfuse-secret-key"
LANGFUSE_HOST=https://cloud.langfuse.com

# LLM Settings
LLM_API_KEY="your-llm-api-key" # e.g. OpenAI API key
LLM_MODEL=gpt-4o-mini
DEFAULT_LLM_TEMPERATURE=0.2

# JWT Settings
JWT_SECRET_KEY="your-jwt-secret-key"
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_DAYS=30

# Database Settings
POSTGRES_URL="postgresql://:your-db-password@POSTGRES_HOST:POSTGRES_PORT/POSTGRES_DB"
POSTGRES_POOL_SIZE=5
POSTGRES_MAX_OVERFLOW=10

# Rate Limiting Settings
RATE_LIMIT_DEFAULT="1000 per day,200 per hour"
RATE_LIMIT_CHAT="100 per minute"
RATE_LIMIT_CHAT_STREAM="100 per minute"
RATE_LIMIT_MESSAGES="200 per minute"
RATE_LIMIT_LOGIN="100 per minute"

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=console

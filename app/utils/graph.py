"""This file contains the graph utilities for the application."""

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import trim_messages as _trim_messages

from app.core.config import settings
from app.schemas import Message


def dump_messages(messages: list[Message]) -> list[dict]:
    """Dump the messages to a list of dictionaries.

    Args:
        messages (list[Message]): The messages to dump.

    Returns:
        list[dict]: The dumped messages.
    """
    return [message.model_dump() for message in messages]


def prepare_messages(messages: list[Message], llm: BaseChatModel, system_prompt: str) -> list[Message]:
    """Prepare the messages for the LLM.

    Args:
        messages (list[Message]): The messages to prepare.
        llm (BaseChatModel): The LLM to use.
        system_prompt (str): The system prompt to use.

    Returns:
        list[Message]: The prepared messages.
    """
    try:
        # Try to trim messages using the LLM's token counter
        trimmed_messages = _trim_messages(
            dump_messages(messages),
            strategy="last",
            token_counter=llm,
            max_tokens=settings.MAX_TOKENS,
            start_on="human",
            include_system=False,
            allow_partial=False,
        )
        
        # Convert trimmed messages to proper format
        processed_messages = []
        for msg in trimmed_messages:
            if isinstance(msg, dict):
                # Already a dictionary
                processed_messages.append(Message(**msg))
            else:
                # LangChain message object - convert to dict first
                if hasattr(msg, 'type') and hasattr(msg, 'content'):
                    role_mapping = {
                        'human': 'user',
                        'ai': 'assistant',
                        'system': 'system'
                    }
                    role = role_mapping.get(msg.type, msg.type)
                    processed_messages.append(Message(role=role, content=msg.content))
                else:
                    # Skip invalid messages
                    continue
                    
    except (AttributeError, ValueError, Exception):
        # Fallback: If token counting fails (e.g., Azure OpenAI model name issues),
        # just use the last few messages up to a reasonable limit
        max_messages = 10  # Keep last 10 messages
        selected_messages = messages[-max_messages:] if len(messages) > max_messages else messages
        processed_messages = selected_messages
        
    return [Message(role="system", content=system_prompt)] + processed_messages

"""This file contains the session model for the application."""

from sqlmodel import Field

from app.models.base import BaseModel


class Session(BaseModel, table=True):
    """Session model for storing chat sessions.

    Attributes:
        id: The primary key (UUID string)
        external_user_id: Reference to user ID from external application
        name: Name of the session (defaults to empty string)
        created_at: When the session was created
    """

    id: str = Field(primary_key=True)
    external_user_id: str = Field(index=True)  # Reference to external app's user ID
    name: str = <PERSON>(default="")

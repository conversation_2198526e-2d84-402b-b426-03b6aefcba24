"""Authentication and authorization endpoints for external app integration.

This module provides endpoints for session management with external authentication.
User registration and login are handled by the external application.
"""

import uuid
from typing import List, Dict, Any

from fastapi import (
    APIRouter,
    Depends,
    Form,
    HTTPException,
    Request,
)

from app.core.config import settings
from app.core.limiter import limiter
from app.core.logging import logger
from app.core.external_auth import get_current_user
from app.models.session import Session
from app.schemas.auth import SessionResponse
from app.services.database import DatabaseService
from app.utils.sanitization import sanitize_string

router = APIRouter()
db_service = DatabaseService()


async def get_current_session(
    user_data: Dict[str, Any] = Depends(get_current_user),
) -> tuple[Session, Dict[str, Any]]:
    """Get the current session and user data.
    
    This function extracts session_id from the request context and validates
    that the session belongs to the authenticated user.
    
    Args:
        user_data: User information from external auth
        
    Returns:
        Tuple of (session, user_data)
        
    Raises:
        HTTPException: If session is invalid or not found
    """
    from fastapi import Request
    from starlette.requests import Request as StarletteRequest
    
    # In a real implementation, you'd extract session_id from the request
    # This is a simplified example - you might get session_id from:
    # - Request headers
    # - Query parameters  
    # - Path parameters
    # - Or include it in the token from your external app
    
    # For now, we'll assume session_id is passed in the request somehow
    # You'll need to adapt this based on your external app's token structure
    
    # This is a placeholder - replace with your actual session identification logic
    raise HTTPException(
        status_code=501,
        detail="Session identification logic needs to be implemented based on your external app's token structure"
    )


@router.post("/session", response_model=SessionResponse)
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS.get("session", ["10/minute"])[0])
async def create_session(
    request: Request,
    user_data: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new chat session for the authenticated user.
    
    Args:
        request: The FastAPI request object for rate limiting
        user_data: User information from external auth
        
    Returns:
        SessionResponse: The session ID and name
    """
    try:
        # Generate a unique session ID
        session_id = str(uuid.uuid4())
        
        # Extract user ID from external auth data
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if not external_user_id:
            raise HTTPException(
                status_code=400,
                detail="User ID not found in authentication data"
            )
        
        # Create session in database
        session = await db_service.create_session(session_id, external_user_id)
        
        logger.info(
            "session_created",
            session_id=session_id,
            external_user_id=external_user_id,
            name=session.name,
        )
        
        return SessionResponse(
            session_id=session_id,
            name=session.name,
            # Note: No token returned as sessions are managed via external auth
        )
        
    except ValueError as ve:
        logger.error("session_creation_failed", error=str(ve), exc_info=True)
        raise HTTPException(status_code=422, detail=str(ve))


@router.patch("/session/{session_id}/name", response_model=SessionResponse)
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS.get("session_update", ["30/minute"])[0])
async def update_session_name(
    request: Request,
    session_id: str,
    name: str = Form(...),
    user_data: Dict[str, Any] = Depends(get_current_user)
):
    """Update a session's name.
    
    Args:
        request: The FastAPI request object for rate limiting
        session_id: The ID of the session to update
        name: The new name for the session
        user_data: User information from external auth
        
    Returns:
        SessionResponse: The updated session information
    """
    try:
        # Sanitize inputs
        sanitized_session_id = sanitize_string(session_id)
        sanitized_name = sanitize_string(name)
        
        # Get the session and verify ownership
        session = await db_service.get_session(sanitized_session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Verify the session belongs to the authenticated user
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if session.external_user_id != external_user_id:
            raise HTTPException(status_code=403, detail="Cannot modify other users' sessions")
        
        # Update the session name
        session = await db_service.update_session_name(sanitized_session_id, sanitized_name)
        
        return SessionResponse(session_id=sanitized_session_id, name=session.name)
        
    except ValueError as ve:
        logger.error("session_update_failed", error=str(ve), session_id=session_id, exc_info=True)
        raise HTTPException(status_code=422, detail=str(ve))


@router.delete("/session/{session_id}")
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS.get("session_delete", ["30/minute"])[0])
async def delete_session(
    request: Request,
    session_id: str,
    user_data: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a session for the authenticated user.
    
    Args:
        request: The FastAPI request object for rate limiting
        session_id: The ID of the session to delete
        user_data: User information from external auth
        
    Returns:
        dict: Success message
    """
    try:
        # Sanitize inputs
        sanitized_session_id = sanitize_string(session_id)
        
        # Get the session and verify ownership
        session = await db_service.get_session(sanitized_session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Verify the session belongs to the authenticated user
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if session.external_user_id != external_user_id:
            raise HTTPException(status_code=403, detail="Cannot delete other users' sessions")
        
        # Delete the session
        await db_service.delete_session(sanitized_session_id)
        
        logger.info("session_deleted", session_id=session_id, external_user_id=external_user_id)
        
        return {"message": "Session deleted successfully"}
        
    except ValueError as ve:
        logger.error("session_deletion_failed", error=str(ve), session_id=session_id, exc_info=True)
        raise HTTPException(status_code=422, detail=str(ve))


@router.get("/sessions", response_model=List[SessionResponse])
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS.get("sessions", ["60/minute"])[0])
async def get_user_sessions(
    request: Request,
    user_data: Dict[str, Any] = Depends(get_current_user)
):
    """Get all sessions for the authenticated user.
    
    Args:
        request: The FastAPI request object for rate limiting
        user_data: User information from external auth
        
    Returns:
        List[SessionResponse]: List of user sessions
    """
    try:
        # Extract user ID from external auth data
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if not external_user_id:
            raise HTTPException(
                status_code=400,
                detail="User ID not found in authentication data"
            )
        
        # Get user sessions
        sessions = await db_service.get_user_sessions_by_external_id(external_user_id)
        
        return [
            SessionResponse(
                session_id=sanitize_string(session.id),
                name=sanitize_string(session.name),
            )
            for session in sessions
        ]
        
    except ValueError as ve:
        logger.error("get_sessions_failed", error=str(ve), exc_info=True)
        raise HTTPException(status_code=422, detail=str(ve))
"""External authentication integration for existing app.

This module handles authentication by validating tokens against an external application.
"""

import httpx
from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional

from app.core.config import settings
from app.core.logging import logger
from app.utils.sanitization import sanitize_string

security = HTTPBearer()


class ExternalAuthService:
    """Service for handling external authentication."""
    
    def __init__(self):
        # Configure your external app's auth validation endpoint
        self.auth_endpoint = settings.EXTERNAL_AUTH_ENDPOINT
        print(f"Using external auth endpoint: {self.auth_endpoint}")
        self.timeout = getattr(settings, 'EXTERNAL_AUTH_TIMEOUT', 10)
    
    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate token against external application.
        
        Args:
            token: The bearer token to validate
            
        Returns:
            Dict containing user information from external app
            
        Raises:
            HTTPException: If token validation fails
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    self.auth_endpoint,
                    headers={"Authorization": f"Bearer {token}"}
                )
                
                if response.status_code == 200:
                    user_data = response.json()
                    logger.info("token_validated", user_id=user_data.get("id"))
                    return user_data
                elif response.status_code == 401:
                    logger.warning("token_invalid", status_code=response.status_code)
                    raise HTTPException(
                        status_code=401,
                        detail="Invalid authentication credentials",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                else:
                    logger.error(
                        "auth_service_error", 
                        status_code=response.status_code,
                        response=response.text
                    )
                    raise HTTPException(
                        status_code=503,
                        detail="Authentication service unavailable",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                    
        except httpx.TimeoutException:
            logger.error("auth_service_timeout")
            raise HTTPException(
                status_code=503,
                detail="Authentication service timeout",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except httpx.RequestError as e:
            logger.error("auth_service_request_error", error=str(e))
            raise HTTPException(
                status_code=503,
                detail="Authentication service unavailable",
                headers={"WWW-Authenticate": "Bearer"},
            )


# Global instance
external_auth_service = ExternalAuthService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> Dict[str, Any]:
    """Get the current user from external authentication.
    
    Args:
        credentials: The HTTP authorization credentials containing the token.
        
    Returns:
        Dict: User information from external app
        
    Raises:
        HTTPException: If the token is invalid or missing.
    """
    try:
        # Sanitize token
        token = sanitize_string(credentials.credentials)
        
        # Validate token with external service
        user_data = await external_auth_service.validate_token(token)
        
        return user_data
        
    except ValueError as ve:
        logger.error("token_validation_failed", error=str(ve), exc_info=True)
        raise HTTPException(
            status_code=422,
            detail="Invalid token format",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_session_with_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> tuple[Any, Dict[str, Any]]:
    """Get current session and user data.
    
    This function validates the user token and then retrieves the session.
    Used for endpoints that need both session and user information.
    
    Args:
        credentials: The HTTP authorization credentials
        
    Returns:
        Tuple of (session, user_data)
        
    Raises:
        HTTPException: If authentication fails
    """
    from app.models.session import Session
    from app.services.database import DatabaseService
    
    try:
        # Get user data from external auth
        user_data = await get_current_user(credentials)
        
        # For session-based endpoints, we need to extract session_id from token
        # This assumes your external app includes session_id in the token or user data
        session_id = user_data.get("session_id")
        if not session_id:
            raise HTTPException(
                status_code=400,
                detail="Session ID not found in token"
            )
        
        # Get session from database
        db_service = DatabaseService()
        session = await db_service.get_session(session_id)
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Session not found"
            )
            
        return session, user_data
        
    except ValueError as ve:
        logger.error("session_auth_failed", error=str(ve), exc_info=True)
        raise HTTPException(
            status_code=422,
            detail="Invalid authentication",
            headers={"WWW-Authenticate": "Bearer"},
        )
"""Assignment agent for processing assignment-based queries.

This agent specializes in understanding and processing assignment queries such as:
- "tasks assigned to <PERSON>"
- "unassigned activities"
- "team tasks"
- "work for <PERSON>"
"""

import re
from typing import Dict, List

from app.core.logging import logger
from .agent_schemas import (
    AgentContext,
    AssignmentContext,
    GraphQLFilter,
    QueryType,
)
from .base_agent import BaseAgent


class AssignmentAgent(BaseAgent):
    """Agent specialized in processing assignment-based queries."""
    
    def _get_agent_type(self) -> QueryType:
        """Return the assignment agent type."""
        return QueryType.ASSIGNMENT
    
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns for assignment queries."""
        return {
            # Direct assignment patterns
            "assigned_to": r"assigned\s+to\s+(\w+)",
            "assigned_by": r"assigned\s+by\s+(\w+)",
            "assignee_name": r"assignee\s+(\w+)",
            
            # Indirect assignment patterns
            "for_person": r"for\s+(\w+)",
            "by_person": r"by\s+(\w+)",
            "person_tasks": r"(\w+)'s\s+tasks",
            "person_work": r"(\w+)'s\s+work",
            
            # Team and group patterns
            "team": r"team\s+(\w+)?",
            "group": r"group\s+(\w+)?",
            "department": r"department\s+(\w+)?",
            "crew": r"crew\s+(\w+)?",
            
            # Unassigned patterns
            "unassigned": r"unassigned|no\s+assignee|without\s+assignee",
            "no_one": r"no\s+one|nobody",
            "open": r"open\s+tasks|available\s+tasks",
            
            # Multiple assignees
            "multiple": r"multiple\s+assignees|shared\s+tasks|collaborative",
            "all_team": r"all\s+team|entire\s+team|whole\s+team",
            
            # Role-based assignments
            "manager": r"manager|supervisor|lead",
            "engineer": r"engineer|developer|technician",
            "contractor": r"contractor|subcontractor",
            
            # Assignment status
            "reassigned": r"reassigned|transferred|moved\s+to",
            "pending_assignment": r"pending\s+assignment|needs\s+assignment",
        }
    
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse assignment context from the query."""
        assignment_context = AssignmentContext()
        query_lower = query.lower()
        
        # Extract assignee names from various patterns
        assignee_names = []
        
        # Direct assignment patterns
        if "assigned_to" in matches:
            assignee_names.extend(matches["assigned_to"])
        if "assignee_name" in matches:
            assignee_names.extend(matches["assignee_name"])
        if "for_person" in matches:
            assignee_names.extend(matches["for_person"])
        if "by_person" in matches:
            assignee_names.extend(matches["by_person"])
        if "person_tasks" in matches:
            assignee_names.extend(matches["person_tasks"])
        if "person_work" in matches:
            assignee_names.extend(matches["person_work"])
        
        # Clean and normalize names
        normalized_names = []
        for name in assignee_names:
            # Remove possessive 's
            clean_name = re.sub(r"'s$", "", name.strip())
            # Capitalize first letter
            clean_name = clean_name.capitalize()
            if clean_name and clean_name not in normalized_names:
                normalized_names.append(clean_name)
        
        assignment_context.assignee_names = normalized_names
        
        # Check for unassigned tasks
        if any(pattern in matches for pattern in ["unassigned", "no_one", "open"]):
            assignment_context.is_unassigned = True
        
        # Check for team queries
        if any(pattern in matches for pattern in ["team", "group", "department", "crew", "multiple", "all_team"]):
            assignment_context.team_query = True
        
        return AgentContext(assignment=assignment_context)
    
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters for assignment queries."""
        filters = []
        
        if not context.assignment:
            return filters
        
        assignment = context.assignment
        
        # Handle specific assignee names
        if assignment.assignee_names:
            if len(assignment.assignee_names) == 1:
                # Single assignee
                filters.append(GraphQLFilter(
                    field="assignees",
                    operator="some",
                    value={"name": {"like": f"%{assignment.assignee_names[0]}%"}},
                    nested_path=["assignees", "name"]
                ))
            else:
                # Multiple assignees - use OR condition
                assignee_conditions = [
                    {"assignees": {"some": {"name": {"like": f"%{name}%"}}}}
                    for name in assignment.assignee_names
                ]
                filters.append(GraphQLFilter(
                    field="or",
                    operator="condition",
                    value=assignee_conditions
                ))
        
        # Handle unassigned tasks
        if assignment.is_unassigned:
            filters.append(GraphQLFilter(
                field="assignees",
                operator="none",
                value={}
            ))
        
        # Handle team queries
        if assignment.team_query and not assignment.assignee_names:
            # For team queries without specific names, we might want tasks with multiple assignees
            filters.append(GraphQLFilter(
                field="assignees",
                operator="some",
                value={}
            ))
        
        return filters
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for assignment queries."""
        return {
            "details": True,
            "assignees": True,  # Always include assignee data for assignment queries
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if assignment query needs coordination with other agents."""
        query_lower = query.lower()
        
        # Look for other domain keywords that suggest multi-domain queries
        other_domain_keywords = [
            # Temporal keywords
            "this month", "next week", "overdue", "today", "quarter",
            # Critical path keywords  
            "critical", "priority", "important", "blocking",
            # Progress keywords
            "completed", "progress", "status", "delayed",
            # Filter keywords
            "project", "containing", "named"
        ]
        
        # Check if query contains assignment + other domain keywords
        has_other_domains = any(keyword in query_lower for keyword in other_domain_keywords)
        
        # Also check for coordination keywords
        coordination_keywords = ["and", "with", "also", "plus", "including"]
        has_coordination_words = any(keyword in query_lower for keyword in coordination_keywords)
        
        return has_other_domains or has_coordination_words
    
    def _calculate_confidence(self, query: str, matches: Dict[str, List[str]]) -> "AgentConfidence":
        """Calculate confidence score for assignment queries."""
        from .agent_schemas import AgentConfidence
        
        if not matches:
            return AgentConfidence(
                score=0.0,
                reasoning="No assignment patterns detected in query",
                matched_patterns=[]
            )
        
        # Base confidence calculation
        match_count = sum(len(match_list) for match_list in matches.values())
        base_confidence = min(match_count * 0.4, 0.9)  # Slightly higher weight for assignment patterns
        
        # Bonus for specific assignee names
        if any(pattern in matches for pattern in ["assigned_to", "assignee_name", "for_person"]):
            base_confidence += 0.2
        
        # Bonus for clear assignment language
        assignment_strength_patterns = ["assigned_to", "assignee_name", "unassigned"]
        strong_matches = sum(1 for pattern in assignment_strength_patterns if pattern in matches)
        if strong_matches > 0:
            base_confidence += strong_matches * 0.1
        
        # Pattern diversity bonus
        pattern_diversity = len(matches) * 0.05
        
        final_score = min(base_confidence + pattern_diversity, 1.0)
        
        matched_pattern_names = list(matches.keys())
        reasoning = f"Found {match_count} assignment indicators across {len(matches)} pattern types"
        
        # Add specific reasoning for strong patterns
        if "assigned_to" in matches:
            reasoning += ", including direct assignment references"
        if "unassigned" in matches:
            reasoning += ", including unassigned task indicators"
        if any(pattern in matches for pattern in ["team", "group"]):
            reasoning += ", including team-based assignment patterns"
        
        return AgentConfidence(
            score=final_score,
            reasoning=reasoning,
            matched_patterns=matched_pattern_names
        )

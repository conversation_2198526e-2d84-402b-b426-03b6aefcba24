"""Progress agent for processing progress and status-based queries.

This agent specializes in understanding and processing progress queries such as:
- "completed tasks"
- "tasks in progress"
- "delayed activities"
- "50% complete"
"""

import re
from typing import Dict, List

from app.core.logging import logger
from .agent_schemas import (
    AgentContext,
    GraphQLFilter,
    ProgressContext,
    QueryType,
)
from .base_agent import BaseAgent


class ProgressAgent(BaseAgent):
    """Agent specialized in processing progress and status queries."""
    
    def _get_agent_type(self) -> QueryType:
        """Return the progress agent type."""
        return QueryType.PROGRESS
    
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns for progress queries."""
        return {
            # Status patterns
            "completed": r"complet(ed|e)|finish(ed|e)|done",
            "in_progress": r"in\s+progress|ongoing|active|current|running",
            "not_started": r"not\s+started|pending|waiting|queued",
            "on_hold": r"on\s+hold|paused|suspended|stopped",
            
            # Percentage patterns
            "percentage": r"(\d{1,3})%|(\d{1,3})\s+percent",
            "half_complete": r"half\s+(complete|done)|50%|fifty\s+percent",
            "quarter_complete": r"quarter\s+(complete|done)|25%|twenty.five\s+percent",
            "three_quarter": r"three.quarter|75%|seventy.five\s+percent",
            "almost_complete": r"almost\s+(complete|done)|nearly\s+(complete|done)|90%|95%",
            
            # Progress indicators
            "no_progress": r"no\s+progress|0%|zero\s+percent|not\s+started",
            "some_progress": r"some\s+progress|partial|partially",
            "good_progress": r"good\s+progress|well\s+along",
            
            # Delay patterns
            "delayed": r"delay(ed|s)?|behind\s+schedule|running\s+late|overdue",
            "on_schedule": r"on\s+schedule|on\s+time|on\s+track",
            "ahead_schedule": r"ahead\s+of\s+schedule|early|ahead",
            
            # Status keywords
            "status": r"status|state|condition",
            "progress": r"progress|advancement|completion",
            
            # Milestone patterns
            "milestone": r"milestone|checkpoint|deliverable",
            "phase": r"phase\s+\d+|stage\s+\d+",
            
            # Duration patterns
            "duration": r"duration|time\s+spent|elapsed",
            "remaining": r"remaining|left|to\s+go",
            
            # Variance patterns
            "variance": r"variance|difference|deviation",
            "baseline": r"baseline|planned|original",
            
            # Range patterns
            "between_percent": r"between\s+(\d{1,3})%\s+and\s+(\d{1,3})%",
            "above_percent": r"above\s+(\d{1,3})%|more\s+than\s+(\d{1,3})%",
            "below_percent": r"below\s+(\d{1,3})%|less\s+than\s+(\d{1,3})%",
        }
    
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse progress context from the query."""
        progress_context = ProgressContext()
        query_lower = query.lower()
        
        # Determine status filter
        if "completed" in matches:
            progress_context.status_filter = "COMPLETED"
        elif "in_progress" in matches:
            progress_context.status_filter = "IN_PROGRESS"
        elif "not_started" in matches:
            progress_context.status_filter = "NOT_STARTED"
        elif "on_hold" in matches:
            progress_context.status_filter = "ON_HOLD"
        
        # Parse percentage ranges
        if "percentage" in matches:
            # Extract specific percentage
            percentages = []
            for match in matches["percentage"]:
                # Handle different capture groups
                if isinstance(match, tuple):
                    for group in match:
                        if group:
                            percentages.append(int(group))
                else:
                    # Extract number from string
                    percent_match = re.search(r'(\d{1,3})', str(match))
                    if percent_match:
                        percentages.append(int(percent_match.group(1)))
            
            if percentages:
                # Use first percentage found
                percent = percentages[0]
                progress_context.completion_min = percent
                progress_context.completion_max = percent
        
        elif "half_complete" in matches:
            progress_context.completion_min = 50
            progress_context.completion_max = 50
        elif "quarter_complete" in matches:
            progress_context.completion_min = 25
            progress_context.completion_max = 25
        elif "three_quarter" in matches:
            progress_context.completion_min = 75
            progress_context.completion_max = 75
        elif "almost_complete" in matches:
            progress_context.completion_min = 90
            progress_context.completion_max = 100
        elif "no_progress" in matches:
            progress_context.completion_min = 0
            progress_context.completion_max = 0
        elif "some_progress" in matches:
            progress_context.completion_min = 1
            progress_context.completion_max = 99
        
        # Handle range patterns
        if "between_percent" in matches:
            range_match = matches["between_percent"][0]
            if isinstance(range_match, tuple) and len(range_match) >= 2:
                try:
                    min_percent = int(range_match[0]) if range_match[0] else 0
                    max_percent = int(range_match[1]) if range_match[1] else 100
                    progress_context.completion_min = min_percent
                    progress_context.completion_max = max_percent
                except (ValueError, IndexError):
                    pass
        
        elif "above_percent" in matches:
            above_match = matches["above_percent"][0]
            if isinstance(above_match, tuple):
                for group in above_match:
                    if group:
                        try:
                            progress_context.completion_min = int(group) + 1
                            break
                        except ValueError:
                            pass
        
        elif "below_percent" in matches:
            below_match = matches["below_percent"][0]
            if isinstance(below_match, tuple):
                for group in below_match:
                    if group:
                        try:
                            progress_context.completion_max = int(group) - 1
                            break
                        except ValueError:
                            pass
        
        # Handle delay patterns
        if any(pattern in matches for pattern in ["delayed", "overdue"]):
            progress_context.has_delays = True
        
        return AgentContext(progress=progress_context)
    
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters for progress queries."""
        filters = []
        
        if not context.progress:
            return filters
        
        progress = context.progress
        
        # Handle status filter
        if progress.status_filter:
            filters.append(GraphQLFilter(
                field="status",
                operator="eq",
                value=progress.status_filter
            ))
        
        # Handle completion percentage filters
        if progress.completion_min is not None and progress.completion_max is not None:
            if progress.completion_min == progress.completion_max:
                # Exact percentage
                filters.append(GraphQLFilter(
                    field="percentComplete",
                    operator="eq",
                    value=progress.completion_min
                ))
            else:
                # Range
                if progress.completion_min > 0:
                    filters.append(GraphQLFilter(
                        field="percentComplete",
                        operator="gte",
                        value=progress.completion_min
                    ))
                if progress.completion_max < 100:
                    filters.append(GraphQLFilter(
                        field="percentComplete",
                        operator="lte",
                        value=progress.completion_max
                    ))
        
        elif progress.completion_min is not None:
            filters.append(GraphQLFilter(
                field="percentComplete",
                operator="gte",
                value=progress.completion_min
            ))
        
        elif progress.completion_max is not None:
            filters.append(GraphQLFilter(
                field="percentComplete",
                operator="lte",
                value=progress.completion_max
            ))
        
        # Handle delay filter
        if progress.has_delays:
            filters.append(GraphQLFilter(
                field="daysDelayed",
                operator="gt",
                value=0
            ))
        
        return filters
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for progress queries."""
        return {
            "details": True,
            "assignees": False,  # Not typically needed for progress queries
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if progress query needs coordination with other agents."""
        query_lower = query.lower()
        
        # Look for other domain keywords that suggest multi-domain queries
        other_domain_keywords = [
            # Temporal keywords
            "this month", "next week", "overdue", "today", "quarter",
            # Assignment keywords
            "assigned", "assignee", "team", "user",
            # Critical path keywords  
            "critical", "priority", "important", "blocking",
            # Filter keywords
            "project", "containing", "named"
        ]
        
        # Check if query contains progress + other domain keywords
        has_other_domains = any(keyword in query_lower for keyword in other_domain_keywords)
        
        # Also check for coordination keywords
        coordination_keywords = ["and", "with", "also", "plus", "including"]
        has_coordination_words = any(keyword in query_lower for keyword in coordination_keywords)
        
        return has_other_domains or has_coordination_words
    
    def _calculate_confidence(self, query: str, matches: Dict[str, List[str]]) -> "AgentConfidence":
        """Calculate confidence score for progress queries."""
        from .agent_schemas import AgentConfidence
        
        if not matches:
            return AgentConfidence(
                score=0.0,
                reasoning="No progress patterns detected in query",
                matched_patterns=[]
            )
        
        # Base confidence calculation
        match_count = sum(len(match_list) for match_list in matches.values())
        base_confidence = min(match_count * 0.35, 0.9)  # Standard weight for progress patterns
        
        # Bonus for status indicators
        status_patterns = ["completed", "in_progress", "not_started", "on_hold"]
        status_matches = sum(1 for pattern in status_patterns if pattern in matches)
        if status_matches > 0:
            base_confidence += status_matches * 0.15
        
        # Bonus for percentage indicators
        percentage_patterns = ["percentage", "half_complete", "quarter_complete", "three_quarter", "almost_complete"]
        percentage_matches = sum(1 for pattern in percentage_patterns if pattern in matches)
        if percentage_matches > 0:
            base_confidence += percentage_matches * 0.2
        
        # Bonus for delay indicators
        delay_patterns = ["delayed", "on_schedule", "ahead_schedule"]
        delay_matches = sum(1 for pattern in delay_patterns if pattern in matches)
        if delay_matches > 0:
            base_confidence += delay_matches * 0.1
        
        # Pattern diversity bonus
        pattern_diversity = len(matches) * 0.05
        
        final_score = min(base_confidence + pattern_diversity, 1.0)
        
        matched_pattern_names = list(matches.keys())
        reasoning = f"Found {match_count} progress indicators across {len(matches)} pattern types"
        
        # Add specific reasoning for strong patterns
        if any(pattern in matches for pattern in status_patterns):
            reasoning += ", including status indicators"
        if any(pattern in matches for pattern in percentage_patterns):
            reasoning += ", including completion percentage references"
        if any(pattern in matches for pattern in delay_patterns):
            reasoning += ", including schedule status patterns"
        if "milestone" in matches:
            reasoning += ", including milestone references"
        
        return AgentConfidence(
            score=final_score,
            reasoning=reasoning,
            matched_patterns=matched_pattern_names
        )

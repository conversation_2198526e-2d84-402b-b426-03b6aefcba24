"""Base agent class defining the common interface for all specialized agents.

This module provides the abstract base class that all specialized agents
(temporal, assignment, critical path, etc.) must inherit from to ensure
consistent behavior and communication protocols.
"""

import re
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Pattern, Tuple

from app.core.logging import logger
from .agent_schemas import (
    AgentConfidence,
    AgentContext,
    AgentResult,
    GraphQLFilter,
    QueryType,
)


class BaseAgent(ABC):
    """Abstract base class for all specialized agents in the swarm.
    
    Each agent is responsible for analyzing user queries within its domain
    (temporal, assignment, critical path, etc.) and producing structured
    results that can be combined with other agents.
    """
    
    def __init__(self):
        """Initialize the base agent with common patterns and metadata."""
        self.agent_type: QueryType = self._get_agent_type()
        self.patterns: Dict[str, Pattern[str]] = self._compile_patterns()
        self.total_queries = 0
        self.total_confidence = 0.0
        self.error_count = 0
        self.last_used: Optional[datetime] = None
        
        logger.info(
            "agent_initialized",
            agent_type=self.agent_type.value,
            pattern_count=len(self.patterns)
        )
    
    @abstractmethod
    def _get_agent_type(self) -> QueryType:
        """Return the agent type for this specialized agent.
        
        Returns:
            QueryType: The type of queries this agent handles
        """
        pass
    
    @abstractmethod
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns specific to this agent's domain.
        
        Returns:
            Dict[str, str]: Mapping of pattern names to regex strings
        """
        pass
    
    @abstractmethod
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse the query and return domain-specific context.
        
        Args:
            query: The user's natural language query
            matches: Dictionary of pattern matches found in the query
            
        Returns:
            AgentContext: Parsed context specific to this agent's domain
        """
        pass
    
    @abstractmethod
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters based on the parsed context.
        
        Args:
            context: The parsed context from the query
            
        Returns:
            List[GraphQLFilter]: List of filters to apply to the GraphQL query
        """
        pass
    
    def _compile_patterns(self) -> Dict[str, Pattern[str]]:
        """Compile regex patterns for efficient matching.
        
        Returns:
            Dict[str, Pattern[str]]: Compiled regex patterns
        """
        raw_patterns = self._get_patterns()
        compiled = {}
        
        for name, pattern_str in raw_patterns.items():
            try:
                compiled[name] = re.compile(pattern_str, re.IGNORECASE)
            except re.error as e:
                logger.error(
                    "pattern_compilation_failed",
                    agent_type=self.agent_type.value,
                    pattern_name=name,
                    pattern=pattern_str,
                    error=str(e)
                )
                # Skip invalid patterns rather than failing
                continue
        
        return compiled
    
    def _find_matches(self, query: str) -> Dict[str, List[str]]:
        """Find all pattern matches in the query.
        
        Args:
            query: The user's natural language query
            
        Returns:
            Dict[str, List[str]]: Dictionary of pattern names to list of matches
        """
        matches = {}
        
        for pattern_name, compiled_pattern in self.patterns.items():
            found_matches = compiled_pattern.findall(query)
            if found_matches:
                matches[pattern_name] = found_matches
        
        return matches
    
    def _calculate_confidence(self, query: str, matches: Dict[str, List[str]]) -> AgentConfidence:
        """Calculate confidence score based on pattern matches.
        
        Args:
            query: The user's natural language query
            matches: Dictionary of pattern matches found
            
        Returns:
            AgentConfidence: Confidence score with reasoning
        """
        if not matches:
            return AgentConfidence(
                score=0.0,
                reasoning=f"No {self.agent_type.value} patterns detected in query",
                matched_patterns=[]
            )
        
        # Base confidence from number of matches
        match_count = sum(len(match_list) for match_list in matches.values())
        base_confidence = min(match_count * 0.3, 0.9)  # Cap at 0.9
        
        # Bonus for multiple different pattern types
        pattern_diversity = len(matches) * 0.1
        
        # Penalty for very short queries with weak matches
        if len(query.split()) < 3 and base_confidence < 0.5:
            base_confidence *= 0.7
        
        final_score = min(base_confidence + pattern_diversity, 1.0)
        
        matched_pattern_names = list(matches.keys())
        reasoning = f"Found {match_count} {self.agent_type.value} indicators across {len(matches)} pattern types"
        
        return AgentConfidence(
            score=final_score,
            reasoning=reasoning,
            matched_patterns=matched_pattern_names
        )
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for this agent type.
        
        Returns:
            Dict[str, bool]: Default includes for GraphQL query
        """
        # Base includes that most agents need
        return {
            "details": True,
            "assignees": False,
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    async def analyze_query(
        self, 
        query: str, 
        project_id: Optional[str] = None
    ) -> AgentResult:
        """Analyze a query and return structured results.
        
        Args:
            query: The user's natural language query
            project_id: Optional project ID for filtering
            
        Returns:
            AgentResult: Structured analysis result
        """
        try:
            self.last_used = datetime.now()
            self.total_queries += 1
            
            # Find pattern matches
            matches = self._find_matches(query)
            
            # Calculate confidence
            confidence = self._calculate_confidence(query, matches)
            
            # Update running average
            self.total_confidence += confidence.score
            
            # Parse context
            context = self._parse_query_context(query, matches)
            
            # Generate filters
            filters = self._generate_filters(context)
            
            # Add project filter if provided
            if project_id:
                filters.append(GraphQLFilter(
                    field="projectId",
                    operator="eq",
                    value=project_id
                ))
            
            # Get includes
            includes = self._get_default_includes()
            
            # Check if coordination is needed
            requires_coordination = self._requires_coordination(query, matches)
            
            result = AgentResult(
                agent_type=self.agent_type,
                confidence=confidence,
                filters=filters,
                includes=includes,
                context=context,
                requires_coordination=requires_coordination,
                metadata={
                    "query_length": len(query),
                    "match_count": sum(len(match_list) for match_list in matches.values()),
                    "pattern_types": list(matches.keys()),
                    "processing_timestamp": datetime.now().isoformat()
                }
            )
            
            logger.info(
                "agent_analysis_completed",
                agent_type=self.agent_type.value,
                confidence=confidence.score,
                filter_count=len(filters),
                requires_coordination=requires_coordination
            )
            
            return result
            
        except Exception as e:
            self.error_count += 1
            logger.error(
                "agent_analysis_failed",
                agent_type=self.agent_type.value,
                error=str(e),
                query=query[:100] + "..." if len(query) > 100 else query
            )
            
            # Return minimal result on error
            return AgentResult(
                agent_type=self.agent_type,
                confidence=AgentConfidence(
                    score=0.0,
                    reasoning=f"Error analyzing query: {str(e)}",
                    matched_patterns=[]
                ),
                filters=[],
                includes=self._get_default_includes(),
                context=AgentContext(),
                requires_coordination=False,
                metadata={"error": str(e)}
            )
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if this query requires coordination with other agents.
        
        Args:
            query: The user's natural language query
            matches: Dictionary of pattern matches found
            
        Returns:
            bool: True if coordination is needed
        """
        # Default implementation - override in specialized agents
        query_words = query.lower().split()
        
        # Look for keywords that suggest multi-domain queries
        coordination_keywords = [
            "and", "also", "with", "plus", "including", "as well as",
            "both", "either", "not only", "but also"
        ]
        
        return any(keyword in query_words for keyword in coordination_keywords)
    
    def get_status(self) -> Dict[str, any]:
        """Get current status and statistics for this agent.
        
        Returns:
            Dict[str, any]: Status information
        """
        average_confidence = (
            self.total_confidence / self.total_queries 
            if self.total_queries > 0 
            else 0.0
        )
        
        return {
            "agent_type": self.agent_type.value,
            "is_active": True,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "total_queries": self.total_queries,
            "average_confidence": round(average_confidence, 3),
            "error_count": self.error_count,
            "pattern_count": len(self.patterns)
        }
    
    def reset_stats(self) -> None:
        """Reset statistics for this agent."""
        self.total_queries = 0
        self.total_confidence = 0.0
        self.error_count = 0
        self.last_used = None
        
        logger.info("agent_stats_reset", agent_type=self.agent_type.value)

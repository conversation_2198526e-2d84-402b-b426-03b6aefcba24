"""Filter agent for processing general filtering and search queries.

This agent specializes in understanding and processing filter queries such as:
- "project ABC tasks"
- "tasks containing 'foundation'"
- "first 10 results"
- "sort by name"
"""

import re
from typing import Dict, List

from app.core.logging import logger
from .agent_schemas import (
    AgentContext,
    FilterContext,
    GraphQLFilter,
    QueryType,
)
from .base_agent import BaseAgent


class FilterAgent(BaseAgent):
    """Agent specialized in processing general filtering and search queries."""
    
    def _get_agent_type(self) -> QueryType:
        """Return the filter agent type."""
        return QueryType.FILTER
    
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns for filter queries."""
        return {
            # Project filtering
            "project_id": r"project\s+([A-Z0-9\-_]+)",
            "project_name": r"project\s+[\"']([^\"']+)[\"']",
            "for_project": r"for\s+project\s+([A-Z0-9\-_]+)",
            
            # Text search patterns
            "containing": r"containing\s+[\"']([^\"']+)[\"']",
            "named": r"named\s+[\"']([^\"']+)[\"']",
            "called": r"called\s+[\"']([^\"']+)[\"']",
            "with_name": r"with\s+name\s+[\"']([^\"']+)[\"']",
            "search_for": r"search\s+for\s+[\"']([^\"']+)[\"']",
            
            # Limit patterns
            "first_n": r"first\s+(\d+)",
            "top_n": r"top\s+(\d+)",
            "limit_n": r"limit\s+(\d+)",
            "only_n": r"only\s+(\d+)",
            "show_n": r"show\s+(\d+)",
            
            # Sorting patterns
            "sort_by": r"sort(ed)?\s+by\s+(\w+)",
            "order_by": r"order(ed)?\s+by\s+(\w+)",
            "arrange_by": r"arrange(d)?\s+by\s+(\w+)",
            
            # Sort direction
            "ascending": r"ascending|asc|a\-z|lowest\s+first",
            "descending": r"descending|desc|z\-a|highest\s+first|latest\s+first",
            
            # ID patterns
            "with_id": r"with\s+id\s+([A-Z0-9\-_]+)",
            "id_equals": r"id\s*=\s*([A-Z0-9\-_]+)",
            
            # WBS patterns
            "wbs": r"wbs\s+([0-9\.]+)",
            "wbs_like": r"wbs\s+like\s+[\"']([^\"']+)[\"']",
            
            # Outline patterns
            "level": r"level\s+(\d+)",
            "outline_level": r"outline\s+level\s+(\d+)",
            "top_level": r"top\s+level|level\s+1",
            
            # Category patterns
            "category": r"category\s+([A-Za-z0-9\-_]+)",
            "type": r"type\s+([A-Za-z0-9\-_]+)",
            
            # Exclude patterns
            "exclude": r"exclude\s+([A-Za-z0-9\-_\s]+)",
            "not_like": r"not\s+like\s+[\"']([^\"']+)[\"']",
            "except": r"except\s+([A-Za-z0-9\-_\s]+)",
            
            # Range patterns
            "between_ids": r"between\s+([A-Z0-9\-_]+)\s+and\s+([A-Z0-9\-_]+)",
            
            # General search terms
            "search_term": r"[\"']([^\"']+)[\"']",
            "keyword": r"keyword\s+([A-Za-z0-9\-_]+)",
        }
    
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse filter context from the query."""
        filter_context = FilterContext()
        query_lower = query.lower()
        
        # Extract project ID
        if "project_id" in matches:
            filter_context.project_id = matches["project_id"][0]
        elif "project_name" in matches:
            filter_context.project_id = matches["project_name"][0]
        elif "for_project" in matches:
            filter_context.project_id = matches["for_project"][0]
        
        # Extract search text
        search_terms = []
        for pattern in ["containing", "named", "called", "with_name", "search_for"]:
            if pattern in matches:
                search_terms.extend(matches[pattern])
        
        # Also check for general search terms in quotes
        if "search_term" in matches:
            search_terms.extend(matches["search_term"])
        
        if search_terms:
            # Use the first search term found
            filter_context.search_text = search_terms[0]
        
        # Extract limit
        for pattern in ["first_n", "top_n", "limit_n", "only_n", "show_n"]:
            if pattern in matches:
                try:
                    filter_context.limit = int(matches[pattern][0])
                    break
                except (ValueError, IndexError):
                    continue
        
        # Extract sort field
        for pattern in ["sort_by", "order_by", "arrange_by"]:
            if pattern in matches:
                if isinstance(matches[pattern][0], tuple):
                    # Extract from tuple (capturing groups)
                    filter_context.sort_field = matches[pattern][0][1] if len(matches[pattern][0]) > 1 else matches[pattern][0][0]
                else:
                    filter_context.sort_field = matches[pattern][0]
                break
        
        # Extract sort direction
        if "descending" in matches:
            filter_context.sort_direction = "DESC"
        elif "ascending" in matches:
            filter_context.sort_direction = "ASC"
        
        return AgentContext(filter=filter_context)
    
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters for filter queries."""
        filters = []
        
        if not context.filter:
            return filters
        
        filter_ctx = context.filter
        
        # Handle project ID filter
        if filter_ctx.project_id:
            filters.append(GraphQLFilter(
                field="projectId",
                operator="eq",
                value=filter_ctx.project_id
            ))
        
        # Handle search text filter
        if filter_ctx.search_text:
            # Create a text search filter that searches across multiple fields
            filters.append(GraphQLFilter(
                field="or",
                operator="condition",
                value=[
                    {"name": {"like": f"%{filter_ctx.search_text}%"}},
                    {"notes": {"like": f"%{filter_ctx.search_text}%"}},
                    {"wbs": {"like": f"%{filter_ctx.search_text}%"}}
                ]
            ))
        
        return filters
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for filter queries."""
        return {
            "details": True,
            "assignees": False,  # Not typically needed for general filtering
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if filter query needs coordination with other agents."""
        query_lower = query.lower()
        
        # Look for other domain keywords that suggest multi-domain queries
        other_domain_keywords = [
            # Temporal keywords
            "this month", "next week", "overdue", "today", "quarter",
            # Assignment keywords
            "assigned", "assignee", "team", "user",
            # Critical path keywords  
            "critical", "priority", "important", "blocking",
            # Progress keywords
            "completed", "progress", "status", "delayed",
        ]
        
        # Check if query contains filter + other domain keywords
        has_other_domains = any(keyword in query_lower for keyword in other_domain_keywords)
        
        # Also check for coordination keywords
        coordination_keywords = ["and", "with", "also", "plus", "including"]
        has_coordination_words = any(keyword in query_lower for keyword in coordination_keywords)
        
        return has_other_domains or has_coordination_words
    
    def _calculate_confidence(self, query: str, matches: Dict[str, List[str]]) -> "AgentConfidence":
        """Calculate confidence score for filter queries."""
        from .agent_schemas import AgentConfidence
        
        if not matches:
            return AgentConfidence(
                score=0.0,
                reasoning="No filter patterns detected in query",
                matched_patterns=[]
            )
        
        # Base confidence calculation
        match_count = sum(len(match_list) for match_list in matches.values())
        base_confidence = min(match_count * 0.3, 0.8)  # Slightly lower base for general filters
        
        # Bonus for specific filter types
        project_patterns = ["project_id", "project_name", "for_project"]
        project_matches = sum(1 for pattern in project_patterns if pattern in matches)
        if project_matches > 0:
            base_confidence += project_matches * 0.15
        
        # Bonus for search patterns
        search_patterns = ["containing", "named", "called", "with_name", "search_for"]
        search_matches = sum(1 for pattern in search_patterns if pattern in matches)
        if search_matches > 0:
            base_confidence += search_matches * 0.2
        
        # Bonus for limit patterns
        limit_patterns = ["first_n", "top_n", "limit_n", "only_n", "show_n"]
        limit_matches = sum(1 for pattern in limit_patterns if pattern in matches)
        if limit_matches > 0:
            base_confidence += limit_matches * 0.1
        
        # Bonus for sort patterns
        sort_patterns = ["sort_by", "order_by", "arrange_by"]
        sort_matches = sum(1 for pattern in sort_patterns if pattern in matches)
        if sort_matches > 0:
            base_confidence += sort_matches * 0.1
        
        # Pattern diversity bonus
        pattern_diversity = len(matches) * 0.05
        
        final_score = min(base_confidence + pattern_diversity, 1.0)
        
        matched_pattern_names = list(matches.keys())
        reasoning = f"Found {match_count} filter indicators across {len(matches)} pattern types"
        
        # Add specific reasoning for strong patterns
        if any(pattern in matches for pattern in project_patterns):
            reasoning += ", including project filtering"
        if any(pattern in matches for pattern in search_patterns):
            reasoning += ", including text search patterns"
        if any(pattern in matches for pattern in limit_patterns):
            reasoning += ", including result limiting"
        if any(pattern in matches for pattern in sort_patterns):
            reasoning += ", including sorting directives"
        
        return AgentConfidence(
            score=final_score,
            reasoning=reasoning,
            matched_patterns=matched_pattern_names
        )

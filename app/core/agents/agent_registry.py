"""Agent registry for managing and discovering specialized agents.

This module provides a centralized registry system for managing all
specialized agents in the swarm, including registration, discovery,
and status monitoring.
"""

from typing import Dict, List, Optional, Type

from app.core.logging import logger
from .agent_schemas import AgentStatus, QueryType
from .base_agent import BaseAgent


class AgentRegistry:
    """Central registry for managing all agents in the swarm."""
    
    def __init__(self):
        """Initialize the agent registry."""
        self._agents: Dict[QueryType, BaseAgent] = {}
        self._agent_classes: Dict[QueryType, Type[BaseAgent]] = {}
        
        logger.info("agent_registry_initialized")
    
    def register_agent_class(
        self, 
        agent_type: QueryType, 
        agent_class: Type[BaseAgent]
    ) -> None:
        """Register an agent class for lazy instantiation.
        
        Args:
            agent_type: Type of agent to register
            agent_class: Agent class to register
        """
        self._agent_classes[agent_type] = agent_class
        logger.info(
            "agent_class_registered",
            agent_type=agent_type.value,
            class_name=agent_class.__name__
        )
    
    def register_agent(self, agent: BaseAgent) -> None:
        """Register an agent instance.
        
        Args:
            agent: Agent instance to register
        """
        agent_type = agent.agent_type
        self._agents[agent_type] = agent
        
        logger.info(
            "agent_registered",
            agent_type=agent_type.value,
            class_name=agent.__class__.__name__
        )
    
    def get_agent(self, agent_type: QueryType) -> Optional[BaseAgent]:
        """Get an agent by type, instantiating if necessary.
        
        Args:
            agent_type: Type of agent to retrieve
            
        Returns:
            BaseAgent: Agent instance or None if not found
        """
        # Return existing instance if available
        if agent_type in self._agents:
            return self._agents[agent_type]
        
        # Try to instantiate from registered class
        if agent_type in self._agent_classes:
            try:
                agent_class = self._agent_classes[agent_type]
                agent_instance = agent_class()
                self._agents[agent_type] = agent_instance
                
                logger.info(
                    "agent_instantiated",
                    agent_type=agent_type.value,
                    class_name=agent_class.__name__
                )
                
                return agent_instance
            except Exception as e:
                logger.error(
                    "agent_instantiation_failed",
                    agent_type=agent_type.value,
                    error=str(e)
                )
                return None
        
        logger.warning(
            "agent_not_found",
            agent_type=agent_type.value,
            available_types=[t.value for t in self._agents.keys()]
        )
        return None
    
    def get_all_agents(self) -> Dict[QueryType, BaseAgent]:
        """Get all registered agents, instantiating any that aren't loaded.
        
        Returns:
            Dict[QueryType, BaseAgent]: All available agents
        """
        # Instantiate any unloaded agents
        for agent_type in self._agent_classes:
            if agent_type not in self._agents:
                self.get_agent(agent_type)  # This will instantiate it
        
        return self._agents.copy()
    
    def get_available_types(self) -> List[QueryType]:
        """Get all available agent types (both loaded and registered classes).
        
        Returns:
            List[QueryType]: List of all available agent types
        """
        all_types = set(self._agents.keys()) | set(self._agent_classes.keys())
        return list(all_types)
    
    def is_agent_available(self, agent_type: QueryType) -> bool:
        """Check if an agent type is available.
        
        Args:
            agent_type: Agent type to check
            
        Returns:
            bool: True if agent is available
        """
        return (agent_type in self._agents or 
                agent_type in self._agent_classes)
    
    def get_agent_status(self, agent_type: QueryType) -> Optional[Dict[str, any]]:
        """Get status for a specific agent.
        
        Args:
            agent_type: Type of agent to get status for
            
        Returns:
            Optional[Dict[str, any]]: Agent status or None if not found
        """
        agent = self.get_agent(agent_type)
        if agent:
            return agent.get_status()
        return None
    
    def get_all_statuses(self) -> Dict[QueryType, Dict[str, any]]:
        """Get status for all agents.
        
        Returns:
            Dict[QueryType, Dict[str, any]]: Status for all agents
        """
        statuses = {}
        for agent_type, agent in self.get_all_agents().items():
            statuses[agent_type] = agent.get_status()
        return statuses
    
    def reset_agent_stats(self, agent_type: Optional[QueryType] = None) -> None:
        """Reset statistics for one or all agents.
        
        Args:
            agent_type: Specific agent to reset, or None for all agents
        """
        if agent_type:
            agent = self.get_agent(agent_type)
            if agent:
                agent.reset_stats()
                logger.info("agent_stats_reset", agent_type=agent_type.value)
        else:
            for agent in self.get_all_agents().values():
                agent.reset_stats()
            logger.info("all_agent_stats_reset")
    
    def unregister_agent(self, agent_type: QueryType) -> bool:
        """Unregister an agent.
        
        Args:
            agent_type: Type of agent to unregister
            
        Returns:
            bool: True if agent was unregistered
        """
        removed = False
        
        if agent_type in self._agents:
            del self._agents[agent_type]
            removed = True
        
        if agent_type in self._agent_classes:
            del self._agent_classes[agent_type]
            removed = True
        
        if removed:
            logger.info("agent_unregistered", agent_type=agent_type.value)
        
        return removed
    
    def clear_registry(self) -> None:
        """Clear all registered agents and classes."""
        agent_count = len(self._agents) + len(self._agent_classes)
        self._agents.clear()
        self._agent_classes.clear()
        
        logger.info("agent_registry_cleared", removed_count=agent_count)
    
    def get_registry_summary(self) -> Dict[str, any]:
        """Get a summary of the registry state.
        
        Returns:
            Dict[str, any]: Registry summary information
        """
        loaded_agents = list(self._agents.keys())
        registered_classes = list(self._agent_classes.keys())
        all_types = set(loaded_agents) | set(registered_classes)
        
        return {
            "total_agent_types": len(all_types),
            "loaded_agents": len(loaded_agents),
            "registered_classes": len(registered_classes),
            "available_types": [t.value for t in sorted(all_types, key=lambda x: x.value)],
            "loaded_agent_types": [t.value for t in sorted(loaded_agents, key=lambda x: x.value)],
            "unloaded_class_types": [t.value for t in sorted(set(registered_classes) - set(loaded_agents), key=lambda x: x.value)]
        }


# Global registry instance
_global_registry: Optional[AgentRegistry] = None


def get_agent_registry() -> AgentRegistry:
    """Get the global agent registry instance.
    
    Returns:
        AgentRegistry: Global registry instance
    """
    global _global_registry
    if _global_registry is None:
        _global_registry = AgentRegistry()
    return _global_registry


def register_agent_class(agent_type: QueryType, agent_class: Type[BaseAgent]) -> None:
    """Register an agent class with the global registry.
    
    Args:
        agent_type: Type of agent to register
        agent_class: Agent class to register
    """
    registry = get_agent_registry()
    registry.register_agent_class(agent_type, agent_class)


def register_agent(agent: BaseAgent) -> None:
    """Register an agent instance with the global registry.
    
    Args:
        agent: Agent instance to register
    """
    registry = get_agent_registry()
    registry.register_agent(agent)


def get_agent(agent_type: QueryType) -> Optional[BaseAgent]:
    """Get an agent from the global registry.
    
    Args:
        agent_type: Type of agent to retrieve
        
    Returns:
        Optional[BaseAgent]: Agent instance or None if not found
    """
    registry = get_agent_registry()
    return registry.get_agent(agent_type)


def get_all_agents() -> Dict[QueryType, BaseAgent]:
    """Get all agents from the global registry.
    
    Returns:
        Dict[QueryType, BaseAgent]: All available agents
    """
    registry = get_agent_registry()
    return registry.get_all_agents()

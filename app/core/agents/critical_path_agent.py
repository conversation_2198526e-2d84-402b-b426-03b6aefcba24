"""Critical path agent for processing critical path and priority queries.

This agent specializes in understanding and processing critical path queries such as:
- "critical path tasks"
- "priority activities"  
- "blocking dependencies"
- "bottleneck tasks"
"""

from typing import Dict, List

from app.core.logging import logger
from .agent_schemas import (
    AgentContext,
    CriticalContext,
    GraphQLFilter,
    QueryType,
)
from .base_agent import BaseAgent


class CriticalPathAgent(BaseAgent):
    """Agent specialized in processing critical path and priority queries."""
    
    def _get_agent_type(self) -> QueryType:
        """Return the critical path agent type."""
        return QueryType.CRITICAL_PATH
    
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns for critical path queries."""
        return {
            # Critical path patterns
            "critical_path": r"critical\s+path",
            "critical_task": r"critical\s+(task|activity|item)",
            "critical": r"\bcritical\b",
            
            # Priority patterns
            "high_priority": r"high\s+priority|priority\s+1|urgent",
            "priority": r"\bpriority\b",
            "important": r"important|essential|key",
            
            # Dependency patterns
            "blocking": r"block(ing|ed)|blocker",
            "dependent": r"dependent|depends\s+on|dependency",
            "predecessor": r"predecessor|before|precedes",
            "successor": r"successor|after|follows",
            
            # Bottleneck patterns
            "bottleneck": r"bottleneck|constraint|limiting",
            "gating": r"gating|gate|milestone",
            
            # Risk patterns
            "risky": r"risky|risk|at\s+risk",
            "red_flag": r"red\s+flag|warning|alert",
            
            # Schedule impact patterns
            "schedule_impact": r"schedule\s+impact|affects\s+schedule",
            "delay_risk": r"delay\s+risk|risk\s+of\s+delay",
            
            # Workflow patterns
            "workflow": r"workflow|process|sequence",
            "chain": r"chain|series|sequence",
            
            # Status patterns
            "must_complete": r"must\s+complete|required|mandatory",
            "cannot_start": r"cannot\s+start|waiting\s+for|blocked\s+by",
        }
    
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse critical path context from the query."""
        critical_context = CriticalContext()
        query_lower = query.lower()
        
        # Determine if query is about critical path tasks
        if any(pattern in matches for pattern in ["critical_path", "critical_task", "critical"]):
            critical_context.is_critical = True
        
        # Determine if query is about priority tasks
        if any(pattern in matches for pattern in ["high_priority", "priority", "important", "must_complete"]):
            critical_context.is_priority = True
        
        # Determine dependency type
        if "blocking" in matches:
            critical_context.dependency_type = "blocking"
        elif "dependent" in matches:
            critical_context.dependency_type = "dependent"
        elif "predecessor" in matches:
            critical_context.dependency_type = "predecessor"
        elif "successor" in matches:
            critical_context.dependency_type = "successor"
        elif "bottleneck" in matches:
            critical_context.dependency_type = "bottleneck"
        elif "gating" in matches:
            critical_context.dependency_type = "gating"
        
        return AgentContext(critical=critical_context)
    
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters for critical path queries."""
        filters = []
        
        if not context.critical:
            return filters
        
        critical = context.critical
        
        # Handle critical path filter
        if critical.is_critical:
            filters.append(GraphQLFilter(
                field="isCritical",
                operator="eq",
                value=True
            ))
        
        # Handle priority filter
        if critical.is_priority:
            filters.append(GraphQLFilter(
                field="isPriority",
                operator="eq",
                value=True
            ))
        
        # Handle dependency-specific filters
        if critical.dependency_type == "blocking":
            # Tasks that have successors (other tasks depend on them)
            filters.append(GraphQLFilter(
                field="successors",
                operator="is_not",
                value=None
            ))
        
        elif critical.dependency_type == "dependent":
            # Tasks that have predecessors (depend on other tasks)
            filters.append(GraphQLFilter(
                field="predecessors",
                operator="is_not",
                value=None
            ))
        
        elif critical.dependency_type == "bottleneck":
            # Tasks that are both critical and have many dependencies
            filters.extend([
                GraphQLFilter(
                    field="isCritical",
                    operator="eq",
                    value=True
                ),
                GraphQLFilter(
                    field="or",
                    operator="condition",
                    value=[
                        {"predecessors": {"is_not": None}},
                        {"successors": {"is_not": None}}
                    ]
                )
            ])
        
        elif critical.dependency_type == "gating":
            # Tasks that are milestones or have high impact
            filters.extend([
                GraphQLFilter(
                    field="or",
                    operator="condition", 
                    value=[
                        {"isCritical": {"eq": True}},
                        {"isPriority": {"eq": True}},
                        {"outlineLevel": {"eq": 1}}  # Top-level tasks often gatings
                    ]
                )
            ])
        
        return filters
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for critical path queries."""
        return {
            "details": True,
            "assignees": True,  # Often need to know who's responsible for critical tasks
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if critical path query needs coordination with other agents."""
        query_lower = query.lower()
        
        # Look for other domain keywords that suggest multi-domain queries
        other_domain_keywords = [
            # Temporal keywords
            "this month", "next week", "overdue", "today", "quarter",
            # Assignment keywords
            "assigned", "assignee", "team", "user",
            # Progress keywords
            "completed", "progress", "status", "delayed",
            # Filter keywords
            "project", "containing", "named"
        ]
        
        # Check if query contains critical path + other domain keywords
        has_other_domains = any(keyword in query_lower for keyword in other_domain_keywords)
        
        # Also check for coordination keywords
        coordination_keywords = ["and", "with", "also", "plus", "including"]
        has_coordination_words = any(keyword in query_lower for keyword in coordination_keywords)
        
        return has_other_domains or has_coordination_words
    
    def _calculate_confidence(self, query: str, matches: Dict[str, List[str]]) -> "AgentConfidence":
        """Calculate confidence score for critical path queries."""
        from .agent_schemas import AgentConfidence
        
        if not matches:
            return AgentConfidence(
                score=0.0,
                reasoning="No critical path patterns detected in query",
                matched_patterns=[]
            )
        
        # Base confidence calculation
        match_count = sum(len(match_list) for match_list in matches.values())
        base_confidence = min(match_count * 0.35, 0.9)  # Standard weight for critical path patterns
        
        # Bonus for strong critical path indicators
        strong_patterns = ["critical_path", "critical_task", "blocking", "bottleneck"]
        strong_matches = sum(1 for pattern in strong_patterns if pattern in matches)
        if strong_matches > 0:
            base_confidence += strong_matches * 0.15
        
        # Bonus for priority indicators
        priority_patterns = ["high_priority", "priority", "important"]
        priority_matches = sum(1 for pattern in priority_patterns if pattern in matches)
        if priority_matches > 0:
            base_confidence += priority_matches * 0.1
        
        # Bonus for dependency-related patterns
        dependency_patterns = ["blocking", "dependent", "predecessor", "successor"]
        dependency_matches = sum(1 for pattern in dependency_patterns if pattern in matches)
        if dependency_matches > 0:
            base_confidence += dependency_matches * 0.1
        
        # Pattern diversity bonus
        pattern_diversity = len(matches) * 0.05
        
        final_score = min(base_confidence + pattern_diversity, 1.0)
        
        matched_pattern_names = list(matches.keys())
        reasoning = f"Found {match_count} critical path indicators across {len(matches)} pattern types"
        
        # Add specific reasoning for strong patterns
        if "critical_path" in matches or "critical_task" in matches:
            reasoning += ", including explicit critical path references"
        if any(pattern in matches for pattern in priority_patterns):
            reasoning += ", including priority indicators"
        if any(pattern in matches for pattern in dependency_patterns):
            reasoning += ", including dependency relationship patterns"
        if "bottleneck" in matches:
            reasoning += ", including bottleneck analysis patterns"
        
        return AgentConfidence(
            score=final_score,
            reasoning=reasoning,
            matched_patterns=matched_pattern_names
        )

"""Agent swarm system for intelligent query processing.

This package contains a sophisticated agent swarm implementation for processing
natural language queries about construction schedules. The system uses specialized
agents that work together to understand complex queries and generate optimized
GraphQL requests.

Key Components:
- BaseAgent: Abstract base class for all specialized agents
- AgentRegistry: Central registry for managing agents
- QueryType: Enumeration of supported query types
- Agent Schemas: Pydantic models for agent communication

Usage:
    from app.core.agents import get_agent_registry, QueryType
    
    registry = get_agent_registry()
    temporal_agent = registry.get_agent(QueryType.TEMPORAL)
"""

from .agent_registry import (
    AgentRegistry,
    get_agent,
    get_agent_registry,
    get_all_agents,
    register_agent,
    register_agent_class,
)
from .agent_schemas import (
    AgentConfidence,
    AgentContext,
    AgentResult,
    AssignmentContext,
    CombinedResult,
    CriticalContext,
    FilterContext,
    GraphQLFilter,
    ProgressContext,
    QueryIntent,
    QueryType,
    TemporalContext,
)
from .base_agent import BaseAgent
from .coordinator import CoordinatorAgent

__all__ = [
    # Core classes
    "BaseAgent",
    "AgentRegistry",
    "CoordinatorAgent",
    
    # Schema types
    "QueryType",
    "AgentConfidence",
    "AgentContext",
    "AgentResult",
    "CombinedResult",
    "QueryIntent",
    "GraphQLFilter",
    
    # Context types
    "TemporalContext",
    "AssignmentContext", 
    "CriticalContext",
    "ProgressContext",
    "FilterContext",
    
    # Registry functions
    "get_agent_registry",
    "get_agent",
    "get_all_agents",
    "register_agent",
    "register_agent_class",
]

"""Temporal agent for processing time-based queries.

This agent specializes in understanding and processing temporal queries such as:
- "tasks starting this month"
- "overdue activities"
- "Q1 2025 schedules"
- "activities finishing next week"
"""

from datetime import datetime, timedelta
from typing import Dict, List

from app.core.logging import logger
from .agent_schemas import (
    AgentContext,
    GraphQLFilter,
    QueryType,
    TemporalContext,
)
from .base_agent import BaseAgent


class TemporalAgent(BaseAgent):
    """Agent specialized in processing temporal/time-based queries."""
    
    def _get_agent_type(self) -> QueryType:
        """Return the temporal agent type."""
        return QueryType.TEMPORAL
    
    def _get_patterns(self) -> Dict[str, str]:
        """Return regex patterns for temporal queries."""
        return {
            # Current time periods
            "current_month": r"(this|current)\s+month",
            "current_week": r"(this|current)\s+week",
            "current_quarter": r"(this|current)\s+quarter",
            "current_year": r"(this|current)\s+year",
            "today": r"today|current\s+day",
            
            # Relative time periods
            "next_month": r"next\s+month",
            "next_week": r"next\s+week",
            "next_quarter": r"next\s+quarter",
            "last_month": r"last\s+month|previous\s+month",
            "last_week": r"last\s+week|previous\s+week",
            
            # Quarters
            "quarter_notation": r"Q[1-4]\s*\d{4}|quarter\s+[1-4]",
            
            # Overdue and delays
            "overdue": r"overdue|past\s+due|behind\s+schedule",
            "delayed": r"delay(ed|s)?|running\s+late",
            
            # Date ranges
            "date_range": r"between\s+.+\s+and\s+.+",
            "before_date": r"before\s+\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}",
            "after_date": r"after\s+\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}",
            
            # Timeline actions
            "starting": r"start(ing|s)?|begin(ning|s)?|commenc(ing|es)?",
            "finishing": r"finish(ing|es)?|end(ing|s)?|complet(ing|es)?",
            "active": r"active|ongoing|in\s+progress|running",
            
            # Specific months
            "month_names": r"(january|february|march|april|may|june|july|august|september|october|november|december)",
            
            # Year references
            "year_reference": r"\d{4}",
        }
    
    def _parse_query_context(self, query: str, matches: Dict[str, List[str]]) -> AgentContext:
        """Parse temporal context from the query."""
        temporal_context = TemporalContext()
        query_lower = query.lower()
        now = datetime.now()
        
        # Determine date type and calculate date ranges
        if "current_month" in matches or "this month" in query_lower:
            temporal_context.date_type = "current_month"
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end_of_month = start_of_month.replace(year=now.year + 1, month=1)
            else:
                end_of_month = start_of_month.replace(month=now.month + 1)
            
            temporal_context.start_date = start_of_month
            temporal_context.end_date = end_of_month
            
        elif "current_week" in matches or "this week" in query_lower:
            temporal_context.date_type = "current_week"
            start_of_week = now - timedelta(days=now.weekday())
            start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_week = start_of_week + timedelta(days=7)
            
            temporal_context.start_date = start_of_week
            temporal_context.end_date = end_of_week
            
        elif "today" in matches:
            temporal_context.date_type = "current_day"
            start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_day = start_of_day + timedelta(days=1)
            
            temporal_context.start_date = start_of_day
            temporal_context.end_date = end_of_day
            
        elif "overdue" in matches:
            temporal_context.date_type = "overdue"
            temporal_context.reference_date = now
            
        elif "current_quarter" in matches or "this quarter" in query_lower:
            temporal_context.date_type = "current_quarter"
            quarter = ((now.month - 1) // 3) + 1
            quarter_start_month = ((quarter - 1) * 3) + 1
            
            start_of_quarter = now.replace(
                month=quarter_start_month, 
                day=1, 
                hour=0, 
                minute=0, 
                second=0, 
                microsecond=0
            )
            
            if quarter == 4:
                end_of_quarter = start_of_quarter.replace(year=now.year + 1, month=1)
            else:
                end_of_quarter = start_of_quarter.replace(month=quarter_start_month + 3)
            
            temporal_context.start_date = start_of_quarter
            temporal_context.end_date = end_of_quarter
            
        elif "quarter_notation" in matches:
            # Parse Q1 2025 format
            quarter_match = matches["quarter_notation"][0]
            temporal_context.date_type = "specific_quarter"
            # For now, set as current quarter - could be enhanced to parse specific quarters
            temporal_context.start_date = now
            temporal_context.end_date = now + timedelta(days=90)
        
        # Determine timeline field based on action words
        if "starting" in matches:
            temporal_context.timeline_field = "baselineStart"
        elif "finishing" in matches:
            temporal_context.timeline_field = "baselineFinish"
        elif "active" in matches:
            temporal_context.timeline_field = "active_period"  # Special handling for active tasks
        else:
            # Default to start date for most queries
            temporal_context.timeline_field = "baselineStart"
        
        return AgentContext(temporal=temporal_context)
    
    def _generate_filters(self, context: AgentContext) -> List[GraphQLFilter]:
        """Generate GraphQL filters for temporal queries."""
        filters = []
        
        if not context.temporal:
            return filters
        
        temporal = context.temporal
        
        if temporal.date_type == "current_month":
            # For "this month" queries, we want tasks that are active during this month
            # This means tasks that have started but haven't finished
            if temporal.start_date and temporal.end_date:
                # Tasks that start before end of month and finish after start of month (or haven't finished)
                filters.extend([
                    GraphQLFilter(
                        field="baselineStart",
                        operator="lt",
                        value=temporal.end_date.isoformat()
                    ),
                    GraphQLFilter(
                        field="or",
                        operator="condition",
                        value=[
                            {"baselineFinish": {"gte": temporal.start_date.isoformat()}},
                            {"baselineFinish": {"is": None}},
                            {"actualFinish": {"is": None}}
                        ]
                    )
                ])
                
        elif temporal.date_type == "current_week":
            if temporal.start_date and temporal.end_date:
                filters.extend([
                    GraphQLFilter(
                        field="baselineStart",
                        operator="gte",
                        value=temporal.start_date.isoformat()
                    ),
                    GraphQLFilter(
                        field="baselineStart",
                        operator="lt",
                        value=temporal.end_date.isoformat()
                    )
                ])
                
        elif temporal.date_type == "current_day":
            if temporal.start_date and temporal.end_date:
                filters.extend([
                    GraphQLFilter(
                        field="baselineStart",
                        operator="gte",
                        value=temporal.start_date.isoformat()
                    ),
                    GraphQLFilter(
                        field="baselineStart",
                        operator="lt",
                        value=temporal.end_date.isoformat()
                    )
                ])
                
        elif temporal.date_type == "overdue":
            if temporal.reference_date:
                # Tasks are overdue if baseline finish is in the past but not actually finished
                filters.extend([
                    GraphQLFilter(
                        field="baselineFinish",
                        operator="lt",
                        value=temporal.reference_date.isoformat()
                    ),
                    GraphQLFilter(
                        field="actualFinish",
                        operator="is",
                        value=None
                    )
                ])
                
        elif temporal.date_type == "current_quarter":
            if temporal.start_date and temporal.end_date:
                filters.extend([
                    GraphQLFilter(
                        field="baselineStart",
                        operator="gte",
                        value=temporal.start_date.isoformat()
                    ),
                    GraphQLFilter(
                        field="baselineStart",
                        operator="lt",
                        value=temporal.end_date.isoformat()
                    )
                ])
        
        # Handle specific timeline fields
        if temporal.timeline_field == "baselineFinish" and temporal.start_date and temporal.end_date:
            # Override previous filters for finish-based queries
            filters = [
                GraphQLFilter(
                    field="baselineFinish",
                    operator="gte",
                    value=temporal.start_date.isoformat()
                ),
                GraphQLFilter(
                    field="baselineFinish",
                    operator="lt",
                    value=temporal.end_date.isoformat()
                )
            ]
        
        return filters
    
    def _get_default_includes(self) -> Dict[str, bool]:
        """Get default includes for temporal queries."""
        return {
            "details": True,
            "assignees": False,  # Not typically needed for temporal queries
            "comments": False,
            "documents": False,
            "medias": False
        }
    
    def _requires_coordination(self, query: str, matches: Dict[str, List[str]]) -> bool:
        """Determine if temporal query needs coordination with other agents."""
        query_lower = query.lower()
        
        # Look for other domain keywords that suggest multi-domain queries
        other_domain_keywords = [
            # Assignment keywords
            "assigned", "assignee", "team", "user",
            # Critical path keywords  
            "critical", "priority", "important", "blocking",
            # Progress keywords
            "completed", "progress", "status", "delayed",
            # Filter keywords
            "project", "containing", "named"
        ]
        
        # Check if query contains temporal + other domain keywords
        has_other_domains = any(keyword in query_lower for keyword in other_domain_keywords)
        
        # Also check for coordination keywords
        coordination_keywords = ["and", "with", "also", "plus", "including"]
        has_coordination_words = any(keyword in query_lower for keyword in coordination_keywords)
        
        return has_other_domains or has_coordination_words

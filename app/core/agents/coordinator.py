"""Coordinator agent for orchestrating the agent swarm.

This module provides the main coordination logic for the agent swarm system,
managing agent selection, parallel execution, result synthesis, and GraphQL
query optimization.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from app.core.logging import logger
from .agent_registry import get_agent_registry
from .agent_schemas import (
    AgentResult,
    CombinedResult,
    GraphQLFilter,
    QueryIntent,
    QueryType,
)
from .assignment_agent import AssignmentAgent
from .critical_path_agent import CriticalPathAgent
from .filter_agent import FilterAgent
from .progress_agent import ProgressAgent
from .temporal_agent import TemporalAgent


class CoordinatorAgent:
    """Main coordinator for the agent swarm system.
    
    This agent orchestrates the entire swarm by:
    1. Analyzing queries to determine relevant agents
    2. Running agents in parallel
    3. Combining and optimizing results
    4. Generating final GraphQL queries
    """
    
    def __init__(self):
        """Initialize the coordinator with all specialized agents."""
        self.registry = get_agent_registry()
        self._initialize_agents()
        
        # Performance tracking
        self.total_queries = 0
        self.total_processing_time = 0.0
        self.agent_usage_stats = {
            QueryType.TEMPORAL: 0,
            QueryType.ASSIGNMENT: 0,
            QueryType.CRITICAL_PATH: 0,
            QueryType.PROGRESS: 0,
            QueryType.FILTER: 0,
        }
        
        logger.info("coordinator_agent_initialized")
    
    def _initialize_agents(self) -> None:
        """Initialize and register all specialized agents."""
        # Register agent classes for lazy loading
        self.registry.register_agent_class(QueryType.TEMPORAL, TemporalAgent)
        self.registry.register_agent_class(QueryType.ASSIGNMENT, AssignmentAgent)
        self.registry.register_agent_class(QueryType.CRITICAL_PATH, CriticalPathAgent)
        self.registry.register_agent_class(QueryType.PROGRESS, ProgressAgent)
        self.registry.register_agent_class(QueryType.FILTER, FilterAgent)
        
        logger.info(
            "agents_registered",
            agent_types=[t.value for t in self.registry.get_available_types()]
        )
    
    async def orchestrate_query(
        self,
        query: str,
        project_id: Optional[str] = None,
        confidence_threshold: float = 0.3
    ) -> QueryIntent:
        """Orchestrate the analysis of a query across all relevant agents.
        
        Args:
            query: The user's natural language query
            project_id: Optional project ID for filtering
            confidence_threshold: Minimum confidence to include an agent
            
        Returns:
            QueryIntent: Complete analysis with combined results
        """
        start_time = datetime.now()
        self.total_queries += 1
        
        try:
            # Step 1: Get confidence scores from all agents
            agent_confidences = await self._get_agent_confidences(query, project_id)
            
            # Step 2: Select relevant agents based on confidence threshold
            relevant_agents = self._select_relevant_agents(agent_confidences, confidence_threshold)
            
            if not relevant_agents:
                # Fallback to filter agent for generic queries
                relevant_agents = [QueryType.FILTER]
                logger.info(
                    "no_relevant_agents_fallback",
                    query=query[:100] + "..." if len(query) > 100 else query,
                    threshold=confidence_threshold
                )
            
            # Step 3: Run selected agents in parallel
            agent_results = await self._run_agents_parallel(query, relevant_agents, project_id)
            
            # Step 4: Combine and optimize results
            combined_result = self._synthesize_results(agent_results, query)
            
            # Step 5: Determine primary agent type
            primary_type = self._determine_primary_type(agent_results)
            
            # Track usage stats
            for agent_type in relevant_agents:
                self.agent_usage_stats[agent_type] += 1
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.total_processing_time += processing_time
            
            # Build final query intent
            query_intent = QueryIntent(
                original_query=query,
                detected_types=relevant_agents,
                primary_type=primary_type,
                combined_result=combined_result,
                processing_time_ms=processing_time
            )
            
            logger.info(
                "query_orchestration_completed",
                query=query[:100] + "..." if len(query) > 100 else query,
                relevant_agents=[t.value for t in relevant_agents],
                primary_type=primary_type.value,
                processing_time_ms=processing_time,
                filter_count=len(combined_result.final_filters)
            )
            
            return query_intent
            
        except Exception as e:
            logger.error(
                "query_orchestration_failed",
                query=query[:100] + "..." if len(query) > 100 else query,
                error=str(e)
            )
            # Return minimal fallback result
            return self._create_fallback_intent(query, str(e))
    
    async def _get_agent_confidences(
        self, 
        query: str, 
        project_id: Optional[str]
    ) -> Dict[QueryType, float]:
        """Get confidence scores from all agents for the query.
        
        Args:
            query: The user's natural language query
            project_id: Optional project ID for filtering
            
        Returns:
            Dict mapping agent types to confidence scores
        """
        confidences = {}
        
        # Get all available agents
        all_agents = self.registry.get_all_agents()
        
        # Analyze query with each agent to get confidence
        tasks = []
        for agent_type, agent in all_agents.items():
            tasks.append(self._get_agent_confidence(agent, query, project_id))
        
        # Run confidence analysis in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        agent_types = list(all_agents.keys())
        for i, result in enumerate(results):
            agent_type = agent_types[i]
            if isinstance(result, Exception):
                logger.error(
                    "agent_confidence_error",
                    agent_type=agent_type.value,
                    error=str(result)
                )
                confidences[agent_type] = 0.0
            else:
                confidences[agent_type] = result
        
        return confidences
    
    async def _get_agent_confidence(
        self, 
        agent, 
        query: str, 
        project_id: Optional[str]
    ) -> float:
        """Get confidence score from a single agent.
        
        Args:
            agent: The agent instance
            query: The user's natural language query
            project_id: Optional project ID for filtering
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        try:
            result = await agent.analyze_query(query, project_id)
            return result.confidence.score
        except Exception as e:
            logger.error(
                "agent_confidence_calculation_failed",
                agent_type=agent.agent_type.value,
                error=str(e)
            )
            return 0.0
    
    def _select_relevant_agents(
        self, 
        confidences: Dict[QueryType, float], 
        threshold: float
    ) -> List[QueryType]:
        """Select agents that meet the confidence threshold.
        
        Args:
            confidences: Dict mapping agent types to confidence scores
            threshold: Minimum confidence to include an agent
            
        Returns:
            List of relevant agent types sorted by confidence
        """
        relevant = [
            agent_type for agent_type, confidence in confidences.items()
            if confidence >= threshold
        ]
        
        # Sort by confidence (highest first)
        relevant.sort(key=lambda t: confidences[t], reverse=True)
        
        return relevant
    
    async def _run_agents_parallel(
        self,
        query: str,
        agent_types: List[QueryType],
        project_id: Optional[str]
    ) -> List[AgentResult]:
        """Run selected agents in parallel.
        
        Args:
            query: The user's natural language query
            agent_types: List of agent types to run
            project_id: Optional project ID for filtering
            
        Returns:
            List of agent results
        """
        tasks = []
        agents = {}
        
        # Prepare tasks for parallel execution
        for agent_type in agent_types:
            agent = self.registry.get_agent(agent_type)
            if agent:
                agents[agent_type] = agent
                tasks.append(agent.analyze_query(query, project_id))
        
        # Execute agents in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        agent_results = []
        for i, result in enumerate(results):
            agent_type = agent_types[i]
            if isinstance(result, Exception):
                logger.error(
                    "agent_execution_error",
                    agent_type=agent_type.value,
                    error=str(result)
                )
                # Skip failed agents
                continue
            else:
                agent_results.append(result)
        
        return agent_results
    
    def _synthesize_results(self, agent_results: List[AgentResult], query: str) -> CombinedResult:
        """Synthesize results from multiple agents into a combined result.
        
        Args:
            agent_results: Results from individual agents
            query: Original query for context
            
        Returns:
            CombinedResult with optimized filters and explanation
        """
        if not agent_results:
            return CombinedResult(
                agent_results=[],
                final_filters=[],
                final_includes={},
                query_explanation="No agents provided valid results for this query",
                confidence_scores={}
            )
        
        # Combine filters from all agents
        all_filters = []
        for result in agent_results:
            all_filters.extend(result.filters)
        
        # Optimize and deduplicate filters
        optimized_filters = self._optimize_filters(all_filters)
        
        # Combine includes (use OR logic - include if any agent wants it)
        combined_includes = {}
        for result in agent_results:
            for key, value in result.includes.items():
                if key not in combined_includes or value:
                    combined_includes[key] = value
        
        # Build confidence scores
        confidence_scores = {
            result.agent_type: result.confidence.score
            for result in agent_results
        }
        
        # Generate explanation
        explanation = self._generate_query_explanation(agent_results, query)
        
        return CombinedResult(
            agent_results=agent_results,
            final_filters=optimized_filters,
            final_includes=combined_includes,
            query_explanation=explanation,
            confidence_scores=confidence_scores,
            execution_metadata={
                "total_agents": len(agent_results),
                "total_filters": len(all_filters),
                "optimized_filters": len(optimized_filters),
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def _optimize_filters(self, filters: List[GraphQLFilter]) -> List[GraphQLFilter]:
        """Optimize and deduplicate GraphQL filters.
        
        Args:
            filters: List of GraphQL filters from agents
            
        Returns:
            Optimized list of filters
        """
        if not filters:
            return []
        
        # Group filters by field
        field_groups = {}
        for filter_item in filters:
            field = filter_item.field
            if field not in field_groups:
                field_groups[field] = []
            field_groups[field].append(filter_item)
        
        optimized = []
        
        for field, field_filters in field_groups.items():
            if len(field_filters) == 1:
                # Single filter for this field
                optimized.append(field_filters[0])
            else:
                # Multiple filters for same field - try to optimize
                optimized_field_filter = self._optimize_field_filters(field, field_filters)
                if optimized_field_filter:
                    optimized.extend(optimized_field_filter)
                else:
                    # Can't optimize, keep all
                    optimized.extend(field_filters)
        
        return optimized
    
    def _optimize_field_filters(
        self, 
        field: str, 
        filters: List[GraphQLFilter]
    ) -> Optional[List[GraphQLFilter]]:
        """Optimize multiple filters for the same field.
        
        Args:
            field: Field name
            filters: List of filters for this field
            
        Returns:
            Optimized filters or None if can't optimize
        """
        # For now, implement basic deduplication
        unique_filters = []
        seen_combinations = set()
        
        for filter_item in filters:
            # Create a key for deduplication
            key = (filter_item.field, filter_item.operator, str(filter_item.value))
            if key not in seen_combinations:
                seen_combinations.add(key)
                unique_filters.append(filter_item)
        
        return unique_filters if unique_filters != filters else None
    
    def _generate_query_explanation(self, agent_results: List[AgentResult], query: str) -> str:
        """Generate human-readable explanation of the query analysis.
        
        Args:
            agent_results: Results from individual agents
            query: Original query
            
        Returns:
            Human-readable explanation string
        """
        if not agent_results:
            return f"Unable to analyze query: '{query}'"
        
        # Get top agents by confidence
        sorted_results = sorted(agent_results, key=lambda r: r.confidence.score, reverse=True)
        top_result = sorted_results[0]
        
        explanation_parts = [
            f"Analyzed query: '{query}'"
        ]
        
        # Primary analysis
        explanation_parts.append(
            f"Primary analysis: {top_result.agent_type.value} (confidence: {top_result.confidence.score:.2f})"
        )
        
        # Additional agents
        if len(sorted_results) > 1:
            additional = [
                f"{r.agent_type.value} ({r.confidence.score:.2f})"
                for r in sorted_results[1:3]  # Show up to 2 additional
            ]
            explanation_parts.append(f"Additional insights: {', '.join(additional)}")
        
        # Filter summary
        total_filters = sum(len(r.filters) for r in agent_results)
        if total_filters > 0:
            explanation_parts.append(f"Generated {total_filters} filter conditions")
        
        return ". ".join(explanation_parts)
    
    def _determine_primary_type(self, agent_results: List[AgentResult]) -> QueryType:
        """Determine the primary query type based on agent results.
        
        Args:
            agent_results: Results from individual agents
            
        Returns:
            Primary query type
        """
        if not agent_results:
            return QueryType.FILTER  # Default fallback
        
        # Return the agent type with highest confidence
        best_result = max(agent_results, key=lambda r: r.confidence.score)
        return best_result.agent_type
    
    def _create_fallback_intent(self, query: str, error: str) -> QueryIntent:
        """Create a fallback query intent for error cases.
        
        Args:
            query: Original query
            error: Error message
            
        Returns:
            Minimal QueryIntent with error info
        """
        from .agent_schemas import AgentConfidence, AgentResult
        
        # Create minimal agent result
        fallback_result = AgentResult(
            agent_type=QueryType.FILTER,
            confidence=AgentConfidence(
                score=0.1,
                reasoning=f"Fallback due to error: {error}",
                matched_patterns=[]
            ),
            filters=[],
            includes={"details": True},
            context=None,
            requires_coordination=False,
            metadata={"error": error}
        )
        
        combined_result = CombinedResult(
            agent_results=[fallback_result],
            final_filters=[],
            final_includes={"details": True},
            query_explanation=f"Error processing query: {error}",
            confidence_scores={QueryType.FILTER: 0.1}
        )
        
        return QueryIntent(
            original_query=query,
            detected_types=[QueryType.FILTER],
            primary_type=QueryType.FILTER,
            combined_result=combined_result,
            processing_time_ms=0.0
        )
    
    def get_performance_stats(self) -> Dict[str, any]:
        """Get performance statistics for the coordinator.
        
        Returns:
            Dict with performance metrics
        """
        avg_processing_time = (
            self.total_processing_time / self.total_queries
            if self.total_queries > 0
            else 0.0
        )
        
        return {
            "total_queries": self.total_queries,
            "average_processing_time_ms": round(avg_processing_time, 2),
            "total_processing_time_ms": round(self.total_processing_time, 2),
            "agent_usage_stats": {
                agent_type.value: count
                for agent_type, count in self.agent_usage_stats.items()
            },
            "registry_summary": self.registry.get_registry_summary()
        }
    
    def reset_stats(self) -> None:
        """Reset all performance statistics."""
        self.total_queries = 0
        self.total_processing_time = 0.0
        self.agent_usage_stats = {agent_type: 0 for agent_type in self.agent_usage_stats}
        
        # Also reset individual agent stats
        self.registry.reset_agent_stats()
        
        logger.info("coordinator_stats_reset")

"""GraphQL client utility for making API calls to Nest.js backend.

This module provides a GraphQL client that can dynamically construct and execute
GraphQL queries based on natural language inputs for construction project data.
"""

import json
from typing import Any, Dict, List, Optional, Union

import aiohttp
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import logger


class GraphQLQuery(BaseModel):
    """GraphQL query model with operation details."""
    
    query: str = Field(description="The GraphQL query string")
    variables: Optional[Dict[str, Any]] = Field(default=None, description="Query variables")
    operation_name: Optional[str] = Field(default=None, description="Operation name")


class GraphQLClient:
    """Client for making GraphQL API calls to Nest.js backend."""
    
    def __init__(self, endpoint: str, timeout: int = 30):
        """Initialize GraphQL client.
        
        Args:
            endpoint: The GraphQL endpoint URL
            timeout: Request timeout in seconds
        """
        self.endpoint = endpoint
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session.
        
        Returns:
            aiohttp.ClientSession: The HTTP session
        """
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
        return self.session
    
    async def execute_query(
        self, 
        query: str, 
        variables: Optional[Dict[str, Any]] = None,
        operation_name: Optional[str] = None,
        auth_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute a GraphQL query.
        
        Args:
            query: The GraphQL query string
            variables: Optional query variables
            operation_name: Optional operation name
            auth_token: Optional authentication token to use for this request
            
        Returns:
            Dict[str, Any]: The GraphQL response
            
        Raises:
            Exception: If the query fails
        """
        session = await self._get_session()
        
        payload = {
            "query": query,
            "variables": variables or {},
        }
        
        if operation_name:
            payload["operationName"] = operation_name
            
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        
        # Add authentication if available (prioritize parameter over settings)
        token = auth_token or (settings.GRAPHQL_AUTH_TOKEN if hasattr(settings, 'GRAPHQL_AUTH_TOKEN') else None)
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        try:
            async with session.post(
                self.endpoint,
                json=payload,
                headers=headers
            ) as response:
                response.raise_for_status()
                result = await response.json()
                
                if "errors" in result:
                    logger.error(
                        "graphql_query_errors",
                        errors=result["errors"],
                        query=query[:200] + "..." if len(query) > 200 else query
                    )
                    raise Exception(f"GraphQL errors: {result['errors']}")
                
                return result
                
        except aiohttp.ClientError as e:
            logger.error(
                "graphql_client_error",
                error=str(e),
                endpoint=self.endpoint
            )
            raise Exception(f"GraphQL request failed: {str(e)}")
    
    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()


class ConstructionScheduleQueryBuilder:
    """Builder class for constructing GraphQL queries for construction schedule data."""
    
    @staticmethod
    def build_schedules_query(
        filter_params: Optional[Dict[str, Any]] = None,
        include_details: bool = True,
        include_assignees: bool = False,
        include_comments: bool = False,
        include_documents: bool = False,
        include_medias: bool = False,
        limit: int = 50,
        offset: int = 0
    ) -> str:
        """Build a query to fetch construction schedules.
        
        Args:
            filter_params: Optional filters for schedules
            include_details: Whether to include detailed schedule fields
            include_assignees: Whether to include assignees
            include_comments: Whether to include comments
            include_documents: Whether to include documents
            include_medias: Whether to include medias
            limit: Maximum number of schedules to return
            offset: Offset for pagination
            
        Returns:
            str: The GraphQL query string
        """
        # Base fields always included
        fields = [
            "id",
            "wbs",
            "name",
            "baselineStart",
            "baselineFinish",
            "actualStart",
            "actualFinish",
            "baselineDuration",
            "predecessors",
            "percentComplete",
            "projectId",
            "suid",
            "outlineLevel",
            "outlineNumber",
            "isCritical",
            "children",
            "status",
            "startVariance",
            "finishVariance",
            "projectScheduleId"
        ]
        
        # Add detailed fields if requested
        if include_details:
            fields.extend([
                "createdBy",
                "updatedBy",
                "createdAt",
                "updatedAt",
                "ownerId",
                "taskMode",
                "percentWorkComplete",
                "proposedStatus",
                "proposedStatusDate",
                "daysDelayed",
                "completedDelay",
                "isTaskPushed",
                "notes",
                "isPriority",
                "predecessorName",
                "proposedPercentComplete",
                "proposedUserId",
                "commentCount"
            ])
        
        # Add related entities if requested
        if include_assignees:
            fields.append("""
            assignees {
                nodes {
                    id
                    name
                    email
                    avatar
                }
            }
            copies {
                nodes {
                    id
                    name
                    email
                    avatar
                }
            }
            """)
        
        if include_comments:
            fields.append("""
            comments {
                nodes {
                    id
                    scheduleId
                    message
                    userId
                }
            }
            """)
        
        if include_documents:
            fields.append("""
            documents {
                id
                name
                type
                category
                status
                requestForSignatures {
                    signById
                    ownerId
                    status
                    id
                    createdAt
                    signBy {
                        name
                        id
                        avatar
                    }
                }
            }
            """)
        
        if include_medias:
            fields.append("""
            medias {
                nodes {
                    id
                    name
                    type
                    url
                }
            }
            """)
        
        # Add updated by user info if details are included
        if include_details:
            fields.append("""
            updatedByUser {
                id
                name
                avatar
            }
            """)
        
        query = f"""
        query GetSchedules($filter: ScheduleFilter, $paging: OffsetPaging, $sorting: [ScheduleSort!]) {{
            schedules(filter: $filter, paging: $paging, sorting: $sorting) {{
                pageInfo {{
                    hasNextPage
                    hasPreviousPage
                }}
                nodes {{
                    {' '.join(fields)}
                }}
            }}
        }}
        """
        
        return query.strip()
    
    @staticmethod
    def build_schedule_search_query(
        project_id: Optional[str] = None,
        status: Optional[str] = None,
        is_critical: Optional[bool] = None,
        is_priority: Optional[bool] = None,
        percent_complete_min: Optional[int] = None,
        percent_complete_max: Optional[int] = None,
        assignee_id: Optional[str] = None,
        search_text: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> str:
        """Build a query to search schedules with various filters.
        
        Args:
            project_id: Optional project ID filter
            status: Optional status filter
            is_critical: Optional filter for critical tasks
            is_priority: Optional filter for priority tasks
            percent_complete_min: Optional minimum percent complete filter
            percent_complete_max: Optional maximum percent complete filter
            assignee_id: Optional assignee ID filter
            search_text: Optional text search in name/notes
            limit: Maximum number of schedules to return
            offset: Offset for pagination
            
        Returns:
            str: The GraphQL query string
        """
        query = """
        query SearchSchedules(
            $filter: ScheduleFilter,
            $paging: OffsetPaging,
            $sorting: [ScheduleSort!]
        ) {
            schedules(filter: $filter, paging: $paging, sorting: $sorting) {
                pageInfo {
                    hasNextPage
                    hasPreviousPage
                }
                nodes {
                    id
                    wbs
                    name
                    baselineStart
                    baselineFinish
                    actualStart
                    actualFinish
                    baselineDuration
                    predecessors
                    percentComplete
                    projectId
                    suid
                    outlineLevel
                    outlineNumber
                    isCritical
                    children
                    status
                    startVariance
                    finishVariance
                    projectScheduleId
                    isPriority
                    daysDelayed
                    notes
                    assignees {
                        nodes {
                            id
                            name
                            avatar
                        }
                    }
                    commentCount
                }
            }
        }
        """
        
        return query.strip()
    
    @staticmethod
    def build_schedule_details_query(
        schedule_id: str,
        include_all_details: bool = True
    ) -> str:
        """Build a query to fetch detailed information about a specific schedule.
        
        Args:
            schedule_id: The ID of the schedule to fetch
            include_all_details: Whether to include all related data
            
        Returns:
            str: The GraphQL query string
        """
        query = """
        query GetScheduleDetails($id: ID!) {
            schedule(id: $id) {
                createdBy
                updatedBy
                createdAt
                updatedAt
                id
                ownerId
                projectId
                projectScheduleId
                suid
                taskMode
                name
                baselineDuration
                baselineStart
                actualStart
                baselineFinish
                actualFinish
                outlineLevel
                outlineNumber
                percentComplete
                percentWorkComplete
                predecessors
                status
                proposedStatus
                proposedStatusDate
                isCritical
                startVariance
                finishVariance
                proposedUserId
                daysDelayed
                completedDelay
                isTaskPushed
                children
                notes
                isPriority
                predecessorName
                wbs
                proposedPercentComplete
                commentCount
                updatedByUser {
                    id
                    name
                    avatar
                }
                assignees {
                    nodes {
                        id
                        name
                        email
                        avatar
                    }
                }
                copies {
                    nodes {
                        id
                        name
                        email
                        avatar
                    }
                }
                comments {
                    nodes {
                        id
                        scheduleId
                        message
                        userId
                    }
                }
                documents {
                    id
                    name
                    type
                    category
                    status
                    requestForSignatures {
                        signById
                        ownerId
                        status
                        id
                        createdAt
                        signBy {
                            name
                            id
                            avatar
                        }
                    }
                }
                medias {
                    nodes {
                        id
                        name
                        type
                        url
                    }
                }
            }
        }
        """
        
        return query.strip()
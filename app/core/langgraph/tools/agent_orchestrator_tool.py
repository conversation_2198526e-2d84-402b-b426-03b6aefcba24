"""Agent Orchestrator Tool for LangGraph.

This tool integrates the CoordinatorAgent to process natural language queries
and generate optimized GraphQL queries for construction schedules.
"""

from langchain_core.tools import BaseTool
from app.core.agents.coordinator import CoordinatorAgent
from app.core.langgraph.tools.graphql_client import GraphQL<PERSON>lient
from app.core.logging import logger

class AgentOrchestratorTool(BaseTool):
    """Tool for orchestrating agent swarm queries."""

    name: str = "schedule_query_agent_swarm"
    description: str = (
        "Intelligent schedule querying using specialized agent swarm. "
        "This tool processes natural language queries and generates optimized GraphQL queries."
    )
    
    def __init__(self):
        """Initialize the tool with a CoordinatorAgent."""
        super().__init__()
        self.coordinator = CoordinatorAgent()
        self.client = GraphQLClient(endpoint="http://localhost:3000/graphql")  # Replace with actual endpoint
    
    async def _arun(self, query: str, schedule_id: str = None, auth_token: str = None, **kwargs) -> str:
        """Run the tool asynchronously.

        Args:
            query (str): The natural language query.
            schedule_id (str, optional): The schedule ID for context.
            auth_token (str, optional): The authentication token for GraphQL API.

        Returns:
            str: The formatted response from the GraphQL query.
        """
        try:
            # Orchestrate query using CoordinatorAgent
            query_intent = await self.coordinator.orchestrate_query(query, project_id=schedule_id)
            
            # Build GraphQL query from intent
            graphql_query = self._build_graphql_from_intent(query_intent)
            
            # Execute GraphQL query
            results = await self.client.execute_query(query=graphql_query, auth_token=auth_token)
            
            # Format and return results
            return self._format_response(results, query_intent)
        except Exception as e:
            logger.error("agent_orchestrator_tool_error", error=str(e))
            return f"Error processing query: {str(e)}"
    
    def _build_graphql_from_intent(self, query_intent):
        """Convert query intent into a GraphQL query."""
        # Placeholder for GraphQL query generation logic
        return "query { schedules { id name } }"
    
    def _format_response(self, results, query_intent):
        """Format the GraphQL results for the user."""
        # Placeholder for response formatting logic
        return f"Results: {results}"

# Create the tool instance
agent_orchestrator_tool = AgentOrchestratorTool()

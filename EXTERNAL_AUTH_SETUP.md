# External Auth Integration Setup

This document explains how to integrate the FastAPI template with your existing application's authentication system.

## Overview

The template has been modified to work with external authentication instead of managing users internally. Here's what changed:

### What was removed:
- User model and database table
- User registration and login endpoints
- JWT token generation for users
- Internal user management

### What was added:
- External auth middleware that validates tokens against your app
- Modified session model to reference external user IDs
- Session-based endpoints that work with external authentication

## Configuration

Set these environment variables:

```bash
# Your app's authentication endpoint
EXTERNAL_AUTH_ENDPOINT=https://your-app.com/api/auth

# Timeout for auth requests (seconds)
EXTERNAL_AUTH_TIMEOUT=10
```

## How It Works

1. **User Authentication**: Your app handles user login/registration and issues tokens
2. **Token Validation**: This template validates tokens by calling your app's auth endpoint
3. **Session Management**: Users create chat sessions that are linked to their external user ID
4. **Chat Operations**: All chat operations require both a valid token and session ownership

## API Endpoints

### Session Management
- `POST /api/v1/auth/session` - Create new chat session
- `GET /api/v1/auth/sessions` - Get user's sessions
- `PATCH /api/v1/auth/session/{session_id}/name` - Update session name
- `DELETE /api/v1/auth/session/{session_id}` - Delete session

### Chat Operations
- `POST /api/v1/chatbot/chat/{session_id}` - Send chat message
- `POST /api/v1/chatbot/chat/{session_id}/stream` - Stream chat response
- `GET /api/v1/chatbot/messages/{session_id}` - Get chat history
- `DELETE /api/v1/chatbot/messages/{session_id}` - Clear chat history

## Required Changes to Your App

Your authentication endpoint should:

1. **Accept**: `GET /api/auth/validate-token` with `Authorization: Bearer <token>` header
2. **Return**: JSON with user information:
   ```json
   {
     "id": "user-123",
     "email": "<EMAIL>",
     "name": "John Doe"
   }
   ```
3. **Status Codes**:
   - `200` - Token valid, return user data
   - `401` - Token invalid
   - `403` - Token expired
   - `500` - Service error

## Example Integration

```python
# In your app's auth endpoint
@app.get("/api/auth/validate-token")
async def validate_token(authorization: str = Header(None)):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid token")
    
    token = authorization.split(" ")[1]
    
    # Validate token (your logic here)
    user = await get_user_from_token(token)
    
    if not user:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    return {
        "id": user.id,
        "email": user.email,
        "name": user.name
    }
```

## Database Migration

Run this SQL to update your database schema:

```sql
-- Remove user table dependencies
ALTER TABLE session DROP CONSTRAINT IF EXISTS session_user_id_fkey;

-- Add external_user_id column
ALTER TABLE session ADD COLUMN external_user_id VARCHAR NOT NULL DEFAULT '';

-- Create index on external_user_id
CREATE INDEX idx_session_external_user_id ON session(external_user_id);

-- Drop old user_id column
ALTER TABLE session DROP COLUMN user_id;

-- Drop user table (if you don't need it)
DROP TABLE user;
```

## Testing

1. Start your main app with auth endpoints
2. Set `EXTERNAL_AUTH_ENDPOINT` to your app's auth URL
3. Get a token from your app
4. Use the token with this template's endpoints:

```bash
# Create session
curl -X POST "http://localhost:8000/api/v1/auth/session" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Chat with session
curl -X POST "http://localhost:8000/api/v1/chatbot/chat/SESSION_ID" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello!"}]}'
```

## Security Notes

- All session operations verify ownership before allowing access
- Tokens are validated on every request
- Session IDs are UUIDs to prevent enumeration
- All inputs are sanitized before processing
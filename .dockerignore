# Version control
.git
.gitignore
.github

# Environment files - these will be passed as build args
.env*
.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
pytestdebug.log
*.egg-info/
*.ipynb

# Virtual environments
.venv
venv
ENV/
env/

# Development tools
.idea
.vscode
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Documentation
docs/
README.md
*.md

# Build artifacts
*.pyc
*.pyo
*.egg-info
dist/
build/

# other
schema.sql

# Reports
evals/reports/

{"dashboard": {"id": null, "uid": "llm-latency", "title": "LLM Inference Latency", "tags": ["inference", "latency"], "timezone": "browser", "schemaVersion": 30, "version": 3, "refresh": "10s", "panels": [{"type": "graph", "title": "LLM Inference Duration (p95)", "targets": [{"expr": "histogram_quantile(0.95, rate(llm_inference_duration_seconds_bucket[1m]))", "legendFormat": "{{model}} (chat)", "refId": "A"}], "datasource": "Prometheus", "gridPos": {"x": 0, "y": 0, "w": 24, "h": 9}}, {"type": "graph", "title": "LLM Stream Inference Duration (p95)", "targets": [{"expr": "histogram_quantile(0.95, rate(llm_stream_duration_seconds_bucket[1m]))", "legendFormat": "{{model}} (stream)", "refId": "B"}], "datasource": "Prometheus", "gridPos": {"x": 0, "y": 9, "w": 24, "h": 9}}, {"type": "graph", "title": "LLM Inference Duration (Average)", "targets": [{"expr": "rate(llm_inference_duration_seconds_sum[1m]) / rate(llm_inference_duration_seconds_count[1m])", "legendFormat": "{{model}} (avg)", "refId": "C"}], "datasource": "Prometheus", "gridPos": {"x": 0, "y": 18, "w": 24, "h": 9}}, {"type": "graph", "title": "LLM Inference Request Count", "targets": [{"expr": "rate(llm_inference_duration_seconds_count[1m])", "legendFormat": "{{model}}", "refId": "D"}], "datasource": "Prometheus", "gridPos": {"x": 0, "y": 27, "w": 24, "h": 9}}]}, "overwrite": true}
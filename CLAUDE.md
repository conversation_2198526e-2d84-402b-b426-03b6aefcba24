# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a production-ready FastAPI template for building AI agent applications with LangGraph integration. It provides a complete foundation for scalable AI agent services with observability, security, and monitoring features.

## Key Architecture Components

### Core Agent System
- **LangGraph Agent**: Main AI workflow engine in `app/core/langgraph/graph.py`
- **LLM Integration**: Uses OpenAI models via LangChain with configurable temperature, tokens, and retry logic
- **Tool System**: Extensible tool framework with DuckDuckGo search integration
- **State Management**: PostgreSQL-based conversation state persistence using LangGraph checkpoints

### Database & Session Management
- **PostgreSQL**: Primary database for user sessions, threads, and conversation history
- **SQLModel**: ORM for database operations with models in `app/models/`
- **Session System**: JWT-based authentication with user sessions and thread management

### Observability & Monitoring
- **Langfuse**: LLM observability and trace monitoring
- **Prometheus**: Metrics collection with custom middleware
- **Grafana**: Pre-configured dashboards for monitoring
- **Structured Logging**: Environment-aware logging with JSON/console formats

### Environment Configuration
- **Multi-Environment**: development, staging, production, test environments
- **Configuration**: Environment-specific settings in `app/core/config.py`
- **Rate Limiting**: Configurable per-endpoint rate limits using SlowAPI

## Common Development Commands

### Environment Setup
```bash
# Install dependencies
make install

# Run development server
make dev

# Run staging server
make staging

# Run production server
make prod
```

### Code Quality
```bash
# Lint code
make lint

# Format code  
make format
```

### Evaluation System
```bash
# Run evaluation with interactive mode
make eval

# Run evaluation with default settings
make eval-quick

# Run evaluation without report generation
make eval-no-report
```

### Docker Commands
```bash
# Build Docker image for environment
make docker-build-env ENV=development

# Run Docker container for environment
make docker-run-env ENV=development

# Start full stack (API, Postgres, Prometheus, Grafana)
make docker-compose-up ENV=development

# Stop full stack
make docker-compose-down ENV=development
```

## Development Patterns

### Environment-Specific Configuration
- Use `.env.development`, `.env.staging`, `.env.production` files
- Environment detection via `APP_ENV` variable
- Automatic fallback to development environment

### LLM Configuration
- Model selection: `LLM_MODEL` (default: gpt-4o-mini)
- Temperature: `DEFAULT_LLM_TEMPERATURE` (default: 0.2)
- Max tokens: `MAX_TOKENS` (default: 2000)
- Retry logic: `MAX_LLM_CALL_RETRIES` (default: 3)

### Azure OpenAI Configuration
- Enable Azure OpenAI: `USE_AZURE_OPENAI` (default: false)
- Azure OpenAI API Key: `AZURE_OPENAI_API_KEY`
- Azure OpenAI Endpoint: `AZURE_OPENAI_ENDPOINT`
- Azure OpenAI API Version: `AZURE_OPENAI_API_VERSION` (default: 2024-02-01)
- Azure OpenAI Deployment Name: `AZURE_OPENAI_DEPLOYMENT_NAME`

### Database Operations
- Connection pooling with environment-specific pool sizes
- Automatic table creation via SQLModel
- Checkpoint tables for conversation state: `checkpoint_blobs`, `checkpoint_writes`, `checkpoints`

### API Structure
- All endpoints under `/api/v1/` prefix
- Rate limiting on all endpoints
- JWT authentication for protected routes
- Structured error responses with validation details

### Evaluation Framework
- Automated model evaluation with configurable metrics
- Langfuse trace analysis integration
- JSON report generation with success/failure metrics
- Interactive CLI with progress bars and colored output

## Key Files and Directories

- `app/main.py`: FastAPI application entry point
- `app/core/langgraph/graph.py`: Main LangGraph agent implementation
- `app/core/config.py`: Environment-specific configuration management
- `app/api/v1/`: API endpoints and routing
- `app/models/`: Database models and schemas
- `evals/`: Model evaluation framework
- `scripts/`: Environment setup and Docker scripts
- `prometheus/`: Metrics collection configuration
- `grafana/`: Pre-configured monitoring dashboards

## Testing and Validation

Always run lint after making changes:
```bash
ruff check .
```

For comprehensive testing, use the evaluation framework to validate model responses and system behavior across different environments.
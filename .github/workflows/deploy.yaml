name: Build and push to Docker Hub

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build-and-push:
    name: Build and push to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install utilities
        run: |
          sudo apt-get update
          sudo apt-get install -y make

      - name: Sanitize repository name
        id: sanitize
        run: |
          REPO_NAME=$(echo "${{ github.event.repository.name }}" | sed 's/^\///' | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g')
          echo "REPO_NAME=${REPO_NAME}" >> $GITHUB_ENV

      - name: Build Image
        run: |
          make docker-build-env ENV=production
          docker tag fastapi-langgraph-template:production ${{ secrets.DOCKER_USERNAME }}/${{ env.REPO_NAME }}:production

      - name: Log in to Docker Hub
        run: |
          echo ${{ secrets.DOCKER_PASSWORD }} | docker login --username ${{ secrets.DOCKER_USERNAME }} --password-stdin

      - name: Push Image
        run: |
          docker push ${{ secrets.DOCKER_USERNAME }}/${{ env.REPO_NAME }}:production
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
